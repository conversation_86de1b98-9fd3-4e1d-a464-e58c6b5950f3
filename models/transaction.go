package models

import (
	"math"

	"gitlab.com/uniqdev/backend/api-membership/core/cast"
)

const (
	OrderTypeDelivery         = "delivery"
	OrderTypePickup           = "pickup"
	OrderTypeSelfOrder        = "self_order"
	OrderTypeDineIn           = "dine_in"
	OrderTypeInternalDelivery = "internal_delivery"
)

type TransactionCustomer struct {
	Name  string `json:"name,omitempty"`
	Phone string `json:"phone,omitempty"`
	Table string `json:"table,omitempty"`
}

type TransactionCreate struct {
	OrderSalesId   string              `json:"order_sales_id,omitempty"`
	OutletID       int                 `json:"outlet_id,omitempty"`
	CustomerName   string              `json:"customer_name,omitempty"`
	Customer       TransactionCustomer `json:"customer,omitempty"`
	OrderList      []OrderList         `json:"order_list,omitempty"`
	OrderType      string              `json:"order_type,omitempty"`
	Shipment       TransactionShipment `json:"shipment,omitempty"`
	Pickup         Pickup              `json:"pickup,omitempty"`
	Status         string              `json:"status,omitempty"`
	Note           string              `json:"note,omitempty"`
	Deal           TransactionPromo    `json:"deal,omitempty"`
	Promotion      []TransactionPromo  `json:"promotion,omitempty"`
	TimeOrder      int64               `json:"time_order,omitempty"`
	OrderConfig    ConfigTransaction   `json:"order_config,omitempty"`
	PaymentTimeout int                 `json:"payment_timeout,omitempty"`
	Message        string              `json:"message,omitempty"`
}

type OrderList struct {
	ProductDetailID int             `json:"product_detail_id,omitempty"`
	Price           int             `json:"price,omitempty"`
	PriceSell       int             `json:"price_sell,omitempty"`
	Qty             int             `json:"qty,omitempty"`
	Note            string          `json:"note,omitempty"`
	Tax             []ItemTax       `json:"tax,omitempty"`
	Product         ItemProduct     `json:"product,omitempty"`
	LinkMenu        []OrderLinkMenu `json:"link_menu,omitempty"`
}

type OrderLinkMenu struct {
	LinkmenuID     int `json:"linkmenu_id,omitempty"`
	ProductFkid    int `json:"product_fkid,omitempty"`
	LinkMenuDetail []struct {
		LinkmenuDetailID  int       `json:"linkmenu_detail_id,omitempty"`
		ProductDetailFkid int       `json:"product_detail_fkid,omitempty"`
		PriceAdd          int       `json:"price_add,omitempty"`
		Tax               []ItemTax `json:"tax,omitempty"`
	} `json:"link_menu_detail,omitempty"`
}

type ItemProduct struct {
	Name string `json:"name,omitempty"`
}

type TransactionShipment struct {
	MemberAddressId int     `json:"member_address_id,omitempty"`
	ShippingAddress string  `json:"shipping_address,omitempty"`
	ShippingCharge  int     `json:"shipping_charge,omitempty"`
	DeliveryTime    int64   `json:"delivery_time,omitempty"` //scheduled delivery time by member
	Latitude        float64 `json:"latitude,omitempty"`
	Longitude       float64 `json:"longitude,omitempty"`
}

type Pickup struct {
	PickupTime string `json:"pickup_time,omitempty"`
}

type ItemTax struct {
	GratuityId int    `json:"gratuity_id,omitempty"`
	Total      int    `json:"total,omitempty"`
	TotalTax   int    `json:"totalTax,omitempty"`
	Name       string `json:"name,omitempty"`
}

type TransactionPromo struct {
	PromotionBuyId int `json:"promotion_buy_id,omitempty"`
	PromotionFkid  int `json:"promotion_fkid,omitempty"`
}

func (t TransactionCreate) CalculateGrandTotal() int {
	result := 0
	for _, item := range t.OrderList {
		result += item.Price * item.Qty

		//adding extra
		for _, link := range item.LinkMenu {
			for _, detail := range link.LinkMenuDetail {
				result += item.Qty * detail.PriceAdd
				//adding tax from extra
				for _, tax := range detail.Tax {
					result += tax.Total
					if tax.Total == 0 {
						result += tax.TotalTax
					}
				}

			}
		}

		//adding tax
		for _, tax := range item.Tax {
			result += tax.Total
			if tax.Total == 0 {
				result += tax.TotalTax
			}
		}
	}

	// for _, promo := range t.Promotions {
	// 	grandTotalPayment -= promo.Value
	// }

	//rounding
	if math.Mod(float64(result), 100) != 0 {
		return int(math.Ceil(float64(result)/100) * 100)
	}

	return result
}

func (t ItemTax) ToSalesOrderTax() SalesOrderTax {
	if t.Total == 0 && t.TotalTax > 0 {
		t.Total = t.TotalTax
	}
	return SalesOrderTax{
		GratuityId: t.GratuityId,
		Total:      t.Total,
		Name:       t.Name,
	}
}

func (t TransactionCreate) ToSalesModel() Sales {
	orderList := make([]SalesOrderList, 0)
	promotions := make([]Promotion, 0)

	for _, item := range t.OrderList {
		taxes := make([]SalesOrderTax, 0)
		for _, tax := range item.Tax {
			taxes = append(taxes, tax.ToSalesOrderTax())
		}

		orderExtra := make([]SalesOrderList, 0)
		for _, link := range item.LinkMenu {
			for _, extra := range link.LinkMenuDetail {
				orderExtra = append(orderExtra, SalesOrderList{
					Price:             extra.PriceAdd,
					Qty:               item.Qty,
					SubTotal:          item.Qty * extra.PriceAdd,
					ProductDetailFkid: extra.ProductDetailFkid,
					ProductFkid:       link.ProductFkid,
					Product: OrderListProduct{
						ProductID:       link.ProductFkid,
						ProductDetailID: extra.ProductDetailFkid,
					},
				})
			}
		}

		orderList = append(orderList, SalesOrderList{
			Price:             item.Price,
			Qty:               item.Qty,
			SubTotal:          item.Price * item.Qty,
			ProductDetailFkid: item.ProductDetailID,
			Note:              item.Note,
			Tax:               taxes,
			Product: OrderListProduct{
				ProductDetailID: item.ProductDetailID,
				Name:            item.Product.Name,
			},
			Extra: orderExtra,
		})
	}

	if t.Deal.PromotionBuyId > 0 {
		promotions = append(promotions, Promotion{
			PromotionId: t.Deal.PromotionFkid,
			Code:        cast.ToString(t.Deal.PromotionBuyId),
		})
	}

	return Sales{
		DisplayNota: t.OrderSalesId,
		GrandTotal:  t.CalculateGrandTotal(),
		OrderList:   orderList,
		OutletId:    t.OutletID,
		OrderType:   t.OrderType,
		Promotions:  promotions,
	}
}
