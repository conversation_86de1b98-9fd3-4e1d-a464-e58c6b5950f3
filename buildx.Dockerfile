# syntax=docker/dockerfile:1.4

# --- Stage 1: Build the Go application ---
FROM golang:alpine AS builder

WORKDIR /src

# 1. Copy go.mod and go.sum first to leverage Docker layer caching.
#    This layer will only be invalidated if your dependencies change.
COPY go.mod go.sum ./

# 2. Download dependencies using a BuildKit cache mount.
#    This command now uses a persistent cache for downloaded modules.
#    Even if the layer is rebuilt, 'go mod download' will be extremely fast
#    as it will find the packages in the mounted cache.
RUN --mount=type=cache,target=/go/pkg/mod \
    go mod download

# 3. Copy the rest of your application's source code.
#    This is done after downloading deps, so changes to your code don't
#    cause a re-download of all dependencies.
COPY . .

# 4. Build the application using a second BuildKit cache mount.
#    THIS IS THE BIGGEST SPEED IMPROVEMENT.
#    The Go compiler maintains its own build cache (`/root/.cache/go-build`).
#    By mounting this directory, Go will only re-compile the specific packages
#    that have changed since the last build. All other compiled code is reused
#    from the cache, making this step incredibly fast on subsequent runs.
RUN --mount=type=cache,target=/root/.cache/go-build \
    CGO_ENABLED=0 GOOS=linux go build -o /app/app-crm server.go

# --- Stage 2: Build the final, minimal production image ---
FROM alpine

# Combine RUN commands to reduce layers.
# Install only the necessary packages for your application to run.
RUN apk add --no-cache ca-certificates tzdata openssl

# Set timezone
ENV TZ=Asia/Jakarta

# Copy necessary files from the host and the builder stage
COPY config/ /config
COPY *.env *.yml /
COPY --from=builder /app/app-crm /app-crm

# Set the entrypoint for the container
ENTRYPOINT ["/app-crm"]