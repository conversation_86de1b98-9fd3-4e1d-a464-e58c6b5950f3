# AI Code Agent Rules for UNIQ API Membership Project

This directory contains comprehensive rules and guidelines for AI code agents working on the UNIQ API Membership project. These rules ensure consistency, maintainability, and adherence to the established architecture patterns.

## Rule Files Overview

### 1. [architecture.md](./architecture.md)
**Core architectural principles and patterns**
- Clean Architecture implementation with Handler/UseCase/Repository layers
- Directory structure and organization
- Layer responsibilities and boundaries
- Dependency injection patterns
- Interface definitions and contracts

### 2. [coding-standards.md](./coding-standards.md)
**Detailed coding standards and best practices**
- Code organization and file naming conventions
- Handler, UseCase, and Repository implementation patterns
- Error handling strategies
- Response formatting standards
- Performance guidelines and caching patterns

### 3. [database-patterns.md](./database-patterns.md)
**Database interaction patterns and security**
- Parameterized query construction using `db.MapParam()`
- Transaction handling with `db.WithTransaction()`
- Common query patterns (pagination, aggregation, joins)
- Caching strategies
- Error handling in database operations

### 4. [development-workflow.md](./development-workflow.md)
**Development processes and workflows**
- Code generation workflow (Analysis → Interface → Implementation)
- New feature development steps
- Modification and maintenance procedures
- Code review checklist
- Testing strategies and patterns

## Quick Reference

### Layer Responsibilities
- **Handler**: HTTP request/response handling only
- **UseCase**: ALL business logic and orchestration
- **Repository**: Database operations only

### Key Patterns
```go
// Database queries
sql := `SELECT * FROM table WHERE admin_id = @adminId AND status = @status`
sql, params := db.MapParam(sql, map[string]any{
    "adminId": user.AdminId,
    "status":  filter.Status,
})

// Transactions
err := db.WithTransaction(func(t db.Transaction) error {
    // Multiple database operations
    return nil
})

// Error handling
return nil, exception.WithCode{Code: 400, Message: "Business rule violation"}
```

### Response Format
```go
// Success
_ = json.NewEncoder(ctx).Encode(models.ApiResponse{
    Status: true,
    Data:   result,
})

// Error
_ = json.NewEncoder(ctx).Encode(models.ApiResponse{
    Status:  false,
    Message: err.Error(),
})
```

## Implementation Order

When implementing new features:

1. **Models** (`models/`) - Define data structures
2. **Domain Interfaces** (`domain/`) - Define contracts
3. **Repository** (`repository/mysql/`) - Implement data access
4. **UseCase** (`usecase/`) - Implement business logic
5. **Handler** (`delivery/http/`) - Implement HTTP layer
6. **Routes** - Register endpoints with appropriate middleware

## Security Requirements

- Always use parameterized queries (`db.MapParam()`)
- Validate user sessions (`domain.UserSessionFastHttp(ctx)`)
- Apply appropriate authentication middleware
- Implement proper authorization checks
- Sanitize and validate all inputs

## Testing Requirements

- Write unit tests for business logic (UseCases)
- Use mocks for dependencies
- Test error scenarios and edge cases
- Include integration tests for critical paths
- Generate mocks using mockery

## Common Mistakes to Avoid

❌ **Don't put business logic in handlers or repositories**
❌ **Don't use string concatenation for SQL queries**
❌ **Don't skip transaction handling for multi-table operations**
❌ **Don't ignore error handling**
❌ **Don't hardcode values**

✅ **Do follow the three-layer architecture strictly**
✅ **Do use parameterized queries always**
✅ **Do use transactions for atomic operations**
✅ **Do handle errors appropriately at each layer**
✅ **Do use configuration and constants**

## Getting Help

When working with this codebase:

1. **Study existing implementations** in similar modules
2. **Follow the established patterns** consistently
3. **Use the database utilities** (`core/db/`) properly
4. **Implement proper error handling** at each layer
5. **Write tests** for your implementations

## Architecture Diagram

```
┌─────────────────┐
│   HTTP Client   │
└─────────┬───────┘
          │
┌─────────▼───────┐
│    Handler      │ ← HTTP concerns only
│  (delivery/http) │
└─────────┬───────┘
          │
┌─────────▼───────┐
│    UseCase      │ ← ALL business logic
│   (usecase/)    │
└─────────┬───────┘
          │
┌─────────▼───────┐
│   Repository    │ ← Database operations only
│ (repository/)   │
└─────────┬───────┘
          │
┌─────────▼───────┐
│    Database     │
└─────────────────┘
```

## Module Structure Example

```
module/transaction/
├── delivery/
│   └── http/
│       ├── transaction_handler.go
│       └── handler_helper.go
├── repository/
│   └── mysql/
│       ├── transaction_repository.go
│       └── transaction_repository_feedback.go
├── usecase/
│   ├── transaction_usecase.go
│   ├── transaction_usecase_feedback.go
│   └── transaction_usecase_helper.go
├── mocks/
│   ├── TransactionRepository.go
│   └── TransactionUseCase.go
├── repository.go (interface)
└── usecase.go (interface)
```

These rules ensure that all AI-generated code follows the established patterns and maintains the high quality and consistency of the UNIQ API Membership project.
