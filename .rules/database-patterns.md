# Database Patterns and Rules for AI Code Agent

## Core Database Principles

### 1. Always Use Parameterized Queries
**NEVER** use string concatenation for SQL queries. Always use the `db.MapParam()` function:

```go
// ✅ CORRECT
sql := `SELECT * FROM admin WHERE id = @salesId AND admin_id = @adminId AND member_id = @memberId`
sql, params := db.MapParam(sql, map[string]any{
    "salesId":  salesId,
    "adminId":  user.AdminId,
    "memberId": user.MemberId,
})
var result models.SalesFeedbackEntity
err := r.db.Prepare(sql, params...).Get(&result)

// ❌ WRONG - SQL Injection Risk
sql := fmt.Sprintf("SELECT * FROM admin WHERE id = %d", salesId)
```

### 2. Transaction Usage
Use `db.WithTransaction()` for operations that require atomicity:

```go
func (r *repository) CreateOrderWithItems(order models.Order, items []models.OrderItem, user domain.UserSession) (int64, error) {
    var orderId int64
    
    err := db.WithTransaction(func(t db.Transaction) error {
        // Insert main order
        result, err := t.Insert("orders", map[string]any{
            "customer_id":  user.MemberId,
            "admin_id":     user.AdminId,
            "total_amount": order.TotalAmount,
            "status":       "pending",
            "created_at":   time.Now().Unix() * 1000,
        })
        if err != nil {
            return err
        }
        
        orderId, _ = result.LastInsertId()
        
        // Insert order items
        for _, item := range items {
            _, err := t.Insert("order_items", map[string]any{
                "order_id":    orderId,
                "product_id":  item.ProductId,
                "quantity":    item.Quantity,
                "price":       item.Price,
            })
            if err != nil {
                return err
            }
        }
        
        // Update inventory
        for _, item := range items {
            _, err := t.Update("products", 
                map[string]any{"stock": fmt.Sprintf("stock - %d", item.Quantity)},
                "id = ?", item.ProductId)
            if err != nil {
                return err
            }
        }
        
        return nil
    })
    
    return orderId, err
}
```

### 3. Query Result Handling
Use appropriate methods for different result types:

```go
// Single record
var result models.Entity
err := r.db.Prepare(sql, params...).Get(&result)

// Multiple records
var results []models.Entity
err := r.db.Prepare(sql, params...).Get(&results)

// Map result (for dynamic queries)
result, err := db.Query(sql, params...)

// Array of maps
results, err := db.QueryArray(sql, params...)
```

## Parameter Binding Patterns

### Named Parameters with @
Use `@paramName` syntax for named parameters:

```go
sql := `
    SELECT p.*, c.name as category_name
    FROM products p
    JOIN categories c ON p.category_id = c.id
    WHERE p.admin_id = @adminId
    AND p.status = @status
    AND p.price BETWEEN @minPrice AND @maxPrice
`

sql, params := db.MapParam(sql, map[string]any{
    "adminId":   user.AdminId,
    "status":    "active",
    "minPrice":  100,
    "maxPrice":  1000,
})
```

### Array Parameters
Handle array parameters for IN clauses:

```go
sql := `SELECT * FROM products WHERE category_id IN @categoryIds AND admin_id = @adminId`

sql, params := db.MapParam(sql, map[string]any{
    "categoryIds": []int{1, 2, 3, 4},
    "adminId":     user.AdminId,
})
// This automatically converts to: WHERE category_id IN (?, ?, ?, ?) AND admin_id = ?
```

### Dynamic WHERE Clauses
Build dynamic queries using placeholder replacement:

```go
func (r *repository) FetchProductsWithFilters(filter domain.ProductFilter, user domain.UserSession) ([]models.Product, error) {
    sql := `
        SELECT p.*, c.name as category_name
        FROM products p
        LEFT JOIN categories c ON p.category_id = c.id
        WHERE p.admin_id = @adminId
        $WHERE
        ORDER BY p.created_at DESC
    `
    
    var whereConditions []string
    paramMap := map[string]any{
        "adminId": user.AdminId,
    }
    
    if filter.Status != "" {
        whereConditions = append(whereConditions, "AND p.status = @status")
        paramMap["status"] = filter.Status
    }
    
    if len(filter.CategoryIds) > 0 {
        whereConditions = append(whereConditions, "AND p.category_id IN @categoryIds")
        paramMap["categoryIds"] = filter.CategoryIds
    }
    
    if filter.MinPrice > 0 {
        whereConditions = append(whereConditions, "AND p.price >= @minPrice")
        paramMap["minPrice"] = filter.MinPrice
    }
    
    if filter.MaxPrice > 0 {
        whereConditions = append(whereConditions, "AND p.price <= @maxPrice")
        paramMap["maxPrice"] = filter.MaxPrice
    }
    
    if filter.SearchTerm != "" {
        whereConditions = append(whereConditions, "AND (p.name LIKE @searchTerm OR p.description LIKE @searchTerm)")
        paramMap["searchTerm"] = "%" + filter.SearchTerm + "%"
    }
    
    whereClause := strings.Join(whereConditions, " ")
    sql = strings.Replace(sql, "$WHERE", whereClause, 1)
    
    sql, params := db.MapParam(sql, paramMap)
    
    var result []models.Product
    err := r.db.Prepare(sql, params...).Get(&result)
    return result, err
}
```

## Common Query Patterns

### Pagination Queries
```go
func (r *repository) FetchWithPagination(filter domain.Filter, page, limit int, user domain.UserSession) ([]models.Entity, int, error) {
    // Count total records
    countSql := `
        SELECT COUNT(*) as cnt 
        FROM entities e 
        WHERE e.admin_id = @adminId 
        AND e.status = @status
    `
    
    countSql, countParams := db.MapParam(countSql, map[string]any{
        "adminId": user.AdminId,
        "status":  filter.Status,
    })
    
    countResult, err := db.Query(countSql, countParams...)
    if err != nil {
        return nil, 0, err
    }
    total := cast.ToInt(countResult["cnt"])
    
    // Fetch paginated data
    offset := (page - 1) * limit
    dataSql := `
        SELECT e.*, related.name as related_name
        FROM entities e
        LEFT JOIN related ON e.related_id = related.id
        WHERE e.admin_id = @adminId 
        AND e.status = @status
        ORDER BY e.created_at DESC
        LIMIT @limit OFFSET @offset
    `
    
    dataSql, dataParams := db.MapParam(dataSql, map[string]any{
        "adminId": user.AdminId,
        "status":  filter.Status,
        "limit":   limit,
        "offset":  offset,
    })
    
    var result []models.Entity
    err = r.db.Prepare(dataSql, dataParams...).Get(&result)
    return result, total, err
}
```

### Aggregation Queries
```go
func (r *repository) FetchSalesStatistics(filter domain.DateFilter, user domain.UserSession) (models.SalesStats, error) {
    sql := `
        SELECT 
            COUNT(*) as total_orders,
            SUM(total_amount) as total_revenue,
            AVG(total_amount) as average_order_value,
            COUNT(DISTINCT customer_id) as unique_customers
        FROM orders 
        WHERE admin_id = @adminId
        AND created_at BETWEEN @startDate AND @endDate
        AND status NOT IN @excludeStatuses
    `
    
    sql, params := db.MapParam(sql, map[string]any{
        "adminId":        user.AdminId,
        "startDate":      filter.StartDate,
        "endDate":        filter.EndDate,
        "excludeStatuses": []string{"cancelled", "refunded"},
    })
    
    result, err := db.Query(sql, params...)
    if err != nil {
        return models.SalesStats{}, err
    }
    
    return models.SalesStats{
        TotalOrders:         cast.ToInt(result["total_orders"]),
        TotalRevenue:        cast.ToFloat64(result["total_revenue"]),
        AverageOrderValue:   cast.ToFloat64(result["average_order_value"]),
        UniqueCustomers:     cast.ToInt(result["unique_customers"]),
    }, nil
}
```

### Complex Joins
```go
func (r *repository) FetchOrdersWithDetails(filter domain.OrderFilter, user domain.UserSession) ([]models.OrderWithDetails, error) {
    sql := `
        SELECT 
            o.id, o.order_number, o.total_amount, o.status, o.created_at,
            c.name as customer_name, c.email as customer_email,
            GROUP_CONCAT(
                CONCAT(p.name, ' (', oi.quantity, 'x', oi.price, ')')
                SEPARATOR ', '
            ) as items_summary,
            COUNT(oi.id) as item_count
        FROM orders o
        JOIN customers c ON o.customer_id = c.id
        LEFT JOIN order_items oi ON o.id = oi.order_id
        LEFT JOIN products p ON oi.product_id = p.id
        WHERE o.admin_id = @adminId
        AND o.created_at BETWEEN @startDate AND @endDate
        $WHERE
        GROUP BY o.id, c.id
        ORDER BY o.created_at DESC
    `
    
    var whereConditions []string
    paramMap := map[string]any{
        "adminId":   user.AdminId,
        "startDate": filter.StartDate,
        "endDate":   filter.EndDate,
    }
    
    if filter.Status != "" {
        whereConditions = append(whereConditions, "AND o.status = @status")
        paramMap["status"] = filter.Status
    }
    
    if filter.CustomerId > 0 {
        whereConditions = append(whereConditions, "AND o.customer_id = @customerId")
        paramMap["customerId"] = filter.CustomerId
    }
    
    whereClause := strings.Join(whereConditions, " ")
    sql = strings.Replace(sql, "$WHERE", whereClause, 1)
    
    sql, params := db.MapParam(sql, paramMap)
    
    var result []models.OrderWithDetails
    err := r.db.Prepare(sql, params...).Get(&result)
    return result, err
}
```

## Transaction Patterns

### Simple Transaction
```go
func (r *repository) UpdateUserProfile(userId int, profile models.UserProfile, user domain.UserSession) error {
    return db.WithTransaction(func(t db.Transaction) error {
        // Update main profile
        _, err := t.Update("users", map[string]any{
            "name":       profile.Name,
            "email":      profile.Email,
            "updated_at": time.Now().Unix() * 1000,
        }, "id = ? AND admin_id = ?", userId, user.AdminId)
        
        if err != nil {
            return err
        }
        
        // Log the change
        _, err = t.Insert("user_activity_log", map[string]any{
            "user_id":     userId,
            "action":      "profile_update",
            "changed_by":  user.MemberId,
            "created_at":  time.Now().Unix() * 1000,
        })
        
        return err
    })
}
```

### Complex Transaction with Rollback Logic
```go
func (r *repository) ProcessPayment(payment models.Payment, user domain.UserSession) error {
    return db.WithTransaction(func(t db.Transaction) error {
        // 1. Create payment record
        paymentResult, err := t.Insert("payments", map[string]any{
            "order_id":     payment.OrderId,
            "amount":       payment.Amount,
            "method":       payment.Method,
            "status":       "processing",
            "admin_id":     user.AdminId,
            "created_at":   time.Now().Unix() * 1000,
        })
        if err != nil {
            return fmt.Errorf("failed to create payment: %w", err)
        }
        
        paymentId, _ := paymentResult.LastInsertId()
        
        // 2. Update order status
        _, err = t.Update("orders", map[string]any{
            "payment_status": "paid",
            "updated_at":     time.Now().Unix() * 1000,
        }, "id = ? AND admin_id = ?", payment.OrderId, user.AdminId)
        if err != nil {
            return fmt.Errorf("failed to update order: %w", err)
        }
        
        // 3. Update inventory
        orderItems, err := r.fetchOrderItems(payment.OrderId, user.AdminId)
        if err != nil {
            return fmt.Errorf("failed to fetch order items: %w", err)
        }
        
        for _, item := range orderItems {
            _, err = t.Update("products", map[string]any{
                "stock": fmt.Sprintf("stock - %d", item.Quantity),
            }, "id = ? AND admin_id = ?", item.ProductId, user.AdminId)
            if err != nil {
                return fmt.Errorf("failed to update inventory for product %d: %w", item.ProductId, err)
            }
        }
        
        // 4. Create audit log
        _, err = t.Insert("payment_audit_log", map[string]any{
            "payment_id":  paymentId,
            "order_id":    payment.OrderId,
            "action":      "payment_processed",
            "amount":      payment.Amount,
            "processed_by": user.MemberId,
            "created_at":  time.Now().Unix() * 1000,
        })
        if err != nil {
            return fmt.Errorf("failed to create audit log: %w", err)
        }
        
        return nil
    })
}
```

## Caching Patterns

### Repository-Level Caching
```go
func (r *repository) FetchCachedCategories(adminId int64) ([]models.Category, error) {
    cacheKey := fmt.Sprintf("categories_%d", adminId)
    
    // Try cache first
    if cached, err := r.cache.Get(cacheKey); err == nil && cached != "" {
        var result []models.Category
        if json.Unmarshal([]byte(cached), &result) == nil {
            return result, nil
        }
    }
    
    // Fallback to database
    sql := `SELECT * FROM categories WHERE admin_id = @adminId AND status = 'active' ORDER BY sort_order`
    sql, params := db.MapParam(sql, map[string]any{"adminId": adminId})
    
    var result []models.Category
    err := r.db.Prepare(sql, params...).Get(&result)
    if err != nil {
        return nil, err
    }
    
    // Cache the result
    if data, err := json.Marshal(result); err == nil {
        r.cache.Set(cacheKey, string(data), 1*time.Hour)
    }
    
    return result, nil
}
```

### Query-Level Caching
```go
func (r *repository) FetchPopularProducts(adminId int64) ([]models.Product, error) {
    sql := `
        SELECT p.*, COUNT(oi.id) as order_count
        FROM products p
        JOIN order_items oi ON p.id = oi.product_id
        JOIN orders o ON oi.order_id = o.id
        WHERE p.admin_id = @adminId
        AND o.created_at > @since
        GROUP BY p.id
        ORDER BY order_count DESC
        LIMIT 10
    `
    
    since := time.Now().AddDate(0, -1, 0).Unix() * 1000 // Last month
    sql, params := db.MapParam(sql, map[string]any{
        "adminId": adminId,
        "since":   since,
    })
    
    var result []models.Product
    err := r.db.QueryCache("popular_products", 2*time.Hour, sql, params...).Model(&result)
    return result, err
}
```

## Error Handling in Database Operations

### Repository Error Handling
```go
func (r *repository) FetchUserById(id int, adminId int64) (models.User, error) {
    sql := `SELECT * FROM users WHERE id = @id AND admin_id = @adminId`
    sql, params := db.MapParam(sql, map[string]any{
        "id":      id,
        "adminId": adminId,
    })
    
    var result models.User
    err := r.db.Prepare(sql, params...).Get(&result)
    if err != nil {
        // Log the error with context
        log.Error("Failed to fetch user: id=%d, adminId=%d, error=%v", id, adminId, err)
        return models.User{}, err
    }
    
    // Check if user was found
    if result.ID == 0 {
        return models.User{}, fmt.Errorf("user not found: id=%d", id)
    }
    
    return result, nil
}
```

### Transaction Error Handling
```go
func (r *repository) TransferFunds(fromAccount, toAccount int, amount float64, user domain.UserSession) error {
    return db.WithTransaction(func(t db.Transaction) error {
        // Check source account balance
        var fromBalance float64
        balanceResult, err := db.Query(
            "SELECT balance FROM accounts WHERE id = ? AND admin_id = ?", 
            fromAccount, user.AdminId,
        )
        if err != nil {
            return fmt.Errorf("failed to check source balance: %w", err)
        }
        fromBalance = cast.ToFloat64(balanceResult["balance"])
        
        if fromBalance < amount {
            return fmt.Errorf("insufficient funds: available=%.2f, required=%.2f", fromBalance, amount)
        }
        
        // Debit source account
        _, err = t.Update("accounts", map[string]any{
            "balance": fmt.Sprintf("balance - %.2f", amount),
        }, "id = ? AND admin_id = ?", fromAccount, user.AdminId)
        if err != nil {
            return fmt.Errorf("failed to debit source account: %w", err)
        }
        
        // Credit destination account
        _, err = t.Update("accounts", map[string]any{
            "balance": fmt.Sprintf("balance + %.2f", amount),
        }, "id = ? AND admin_id = ?", toAccount, user.AdminId)
        if err != nil {
            return fmt.Errorf("failed to credit destination account: %w", err)
        }
        
        // Create transaction log
        _, err = t.Insert("transaction_log", map[string]any{
            "from_account": fromAccount,
            "to_account":   toAccount,
            "amount":       amount,
            "type":         "transfer",
            "created_by":   user.MemberId,
            "created_at":   time.Now().Unix() * 1000,
        })
        if err != nil {
            return fmt.Errorf("failed to log transaction: %w", err)
        }
        
        return nil
    })
}
```
