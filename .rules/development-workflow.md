# Development Workflow Rules for AI Code Agent

## Code Generation Workflow

### 1. Analysis Phase
Before writing any code:
- Understand the business requirement completely
- Identify which layer(s) need changes
- Check existing similar implementations for patterns
- Identify dependencies and interfaces needed

### 2. Interface-First Development
Always start with interfaces:
1. Define domain interfaces in `domain/{module}.go`
2. Define module-specific interfaces in `module/{module}/usecase.go` and `repository.go`
3. Implement the interfaces in respective layers

### 3. Layer Implementation Order
Implement in this order:
1. **Models** (`models/`) - Data structures first
2. **Repository** (`repository/mysql/`) - Data access layer
3. **UseCase** (`usecase/`) - Business logic layer  
4. **Handler** (`delivery/http/`) - HTTP layer last

### 4. Testing Strategy
- Write unit tests for business logic (UseCases)
- Use mocks for dependencies
- Test error scenarios
- Integration tests for critical paths

## New Feature Development

### Creating a New Module
1. Create directory structure:
```bash
mkdir -p module/{module_name}/{delivery/http,repository/mysql,usecase}
```

2. Create interface files:
```bash
touch module/{module_name}/usecase.go
touch module/{module_name}/repository.go
```

3. Create domain interface:
```bash
touch domain/{module_name}.go
```

4. Create models if needed:
```bash
touch models/{entity_name}.go
```

### Adding New Endpoint
1. **Define the contract** in domain interface
2. **Implement repository method** if data access needed
3. **Implement usecase method** with business logic
4. **Add handler method** for HTTP handling
5. **Register route** in appropriate router file
6. **Add authentication middleware** if required

### Example: Adding a New Endpoint

#### Step 1: Domain Interface
```go
// domain/product.go
type ProductUseCase interface {
    // existing methods...
    FetchProductReviews(productId int, user UserSession) ([]ProductReview, error)
}
```

#### Step 2: Repository Method
```go
// module/product/repository.go
type Repository interface {
    // existing methods...
    FetchProductReviews(productId int, adminId int64) ([]models.ProductReview, error)
}

// module/product/repository/mysql/product_repository.go
func (r *productRepository) FetchProductReviews(productId int, adminId int64) ([]models.ProductReview, error) {
    sql := `SELECT * FROM product_reviews WHERE product_id = @productId AND admin_id = @adminId`
    sql, params := db.MapParam(sql, map[string]any{
        "productId": productId,
        "adminId":   adminId,
    })
    
    var result []models.ProductReview
    err := r.db.Prepare(sql, params...).Get(&result)
    return result, err
}
```

#### Step 3: UseCase Method
```go
// module/product/usecase/product_usecase.go
func (u *productUseCase) FetchProductReviews(productId int, user domain.UserSession) ([]ProductReview, error) {
    // Business validation
    if productId <= 0 {
        return nil, exception.WithCode{Code: 400, Message: "Invalid product ID"}
    }
    
    // Check if product exists and user has access
    product, err := u.repo.FetchProductById(productId, user.AdminId)
    if err != nil {
        return nil, err
    }
    if product.ID == 0 {
        return nil, exception.WithCode{Code: 404, Message: "Product not found"}
    }
    
    // Fetch reviews
    reviews, err := u.repo.FetchProductReviews(productId, user.AdminId)
    if err != nil {
        return nil, err
    }
    
    // Transform to domain objects
    result := make([]ProductReview, len(reviews))
    for i, review := range reviews {
        result[i] = ProductReview{
            ID:      review.ID,
            Rating:  review.Rating,
            Comment: review.Comment,
            // ... other fields
        }
    }
    
    return result, nil
}
```

#### Step 4: Handler Method
```go
// module/product/delivery/http/product_handler.go
func (h *productHandler) FetchProductReviews(ctx *fasthttp.RequestCtx) {
    user := domain.UserSessionFastHttp(ctx)
    productId := cast.ToInt(ctx.UserValue("id"))
    
    result, err := h.uc.FetchProductReviews(productId, user)
    if err != nil {
        if withCode, ok := err.(exception.WithCode); ok {
            ctx.SetStatusCode(withCode.Code)
            _ = json.NewEncoder(ctx).Encode(models.ApiResponse{
                Status: false, 
                Message: withCode.Message,
            })
        } else {
            ctx.SetStatusCode(fasthttp.StatusInternalServerError)
            _ = json.NewEncoder(ctx).Encode(models.ApiResponse{
                Status: false, 
                Message: err.Error(),
            })
        }
        return
    }
    
    _ = json.NewEncoder(ctx).Encode(models.ApiResponse{
        Status: true, 
        Data: result,
    })
}
```

#### Step 5: Register Route
```go
// In NewHttpProductHandler function
app.GET("/v1/product/:id/reviews", auth.ValidatePublicKey(handler.FetchProductReviews))
```

## Modification Workflow

### Modifying Existing Features
1. **Identify impact scope** - which layers need changes
2. **Update interfaces first** if method signatures change
3. **Implement changes bottom-up** (Repository → UseCase → Handler)
4. **Update tests** to reflect changes
5. **Test thoroughly** including edge cases

### Database Schema Changes
1. **Create migration scripts** (if applicable)
2. **Update models** to reflect schema changes
3. **Update repository queries** as needed
4. **Test with existing data** to ensure compatibility

### Breaking Changes
1. **Version the API** if public interface changes
2. **Maintain backward compatibility** when possible
3. **Document breaking changes** clearly
4. **Provide migration guide** for consumers

## Code Review Checklist

### Architecture Compliance
- [ ] Business logic is in UseCase layer only
- [ ] Database queries are in Repository layer only
- [ ] Handlers only handle HTTP concerns
- [ ] Proper dependency injection used
- [ ] Interfaces are properly defined and implemented

### Code Quality
- [ ] Error handling is appropriate for each layer
- [ ] Logging is meaningful and structured
- [ ] No hardcoded values (use constants/config)
- [ ] Proper parameter validation
- [ ] Resource cleanup (database connections, etc.)

### Security
- [ ] User session validation
- [ ] SQL injection prevention (parameterized queries)
- [ ] Authorization checks where needed
- [ ] Input sanitization
- [ ] Sensitive data handling

### Performance
- [ ] Database queries are optimized
- [ ] Appropriate use of caching
- [ ] No N+1 query problems
- [ ] Memory usage is reasonable
- [ ] Async operations for non-critical tasks

### Testing
- [ ] Unit tests for business logic
- [ ] Error scenarios are tested
- [ ] Mock usage is appropriate
- [ ] Integration tests for critical paths

## Common Patterns

### Pagination
```go
// In domain
type PaginationRequest struct {
    Page  int `json:"page"`
    Limit int `json:"limit"`
}

type PaginationResponse struct {
    Data       interface{} `json:"data"`
    Page       int         `json:"page"`
    Limit      int         `json:"limit"`
    Total      int         `json:"total"`
    TotalPages int         `json:"total_pages"`
}

// In repository
func (r *repository) FetchWithPagination(filter Filter, pagination PaginationRequest) ([]Entity, int, error) {
    // Count query
    countSql := `SELECT COUNT(*) as cnt FROM entities WHERE admin_id = @adminId`
    countResult, err := db.Query(countSql, filter.AdminId)
    if err != nil {
        return nil, 0, err
    }
    total := cast.ToInt(countResult["cnt"])
    
    // Data query with limit/offset
    offset := (pagination.Page - 1) * pagination.Limit
    sql := `SELECT * FROM entities WHERE admin_id = @adminId LIMIT @limit OFFSET @offset`
    sql, params := db.MapParam(sql, map[string]any{
        "adminId": filter.AdminId,
        "limit":   pagination.Limit,
        "offset":  offset,
    })
    
    var result []Entity
    err = r.db.Prepare(sql, params...).Get(&result)
    return result, total, err
}
```

### Bulk Operations
```go
func (r *repository) BulkInsert(entities []models.Entity, user domain.UserSession) error {
    return db.WithTransaction(func(t db.Transaction) error {
        data := make([]map[string]interface{}, len(entities))
        for i, entity := range entities {
            data[i] = map[string]interface{}{
                "name":       entity.Name,
                "admin_id":   user.AdminId,
                "created_by": user.MemberId,
                "created_at": time.Now().Unix() * 1000,
            }
        }
        
        _, err := t.InsertBatch("entities", data)
        return err
    })
}
```

### Async Processing
```go
// In UseCase
func (u *useCase) ProcessWithNotification(data ProcessData, user domain.UserSession) error {
    // Synchronous processing
    result, err := u.processData(data, user)
    if err != nil {
        return err
    }
    
    // Async notifications
    go u.sendNotification(user, result)
    go u.updateAnalytics(data, result)
    
    return nil
}
```

## Debugging Guidelines

### Logging for Debugging
```go
// Add context to logs
log.Info("Processing request: userId=%d, action=%s, params=%v", 
    user.MemberId, action, utils.SimplyToJson(params))

// Log before external calls
log.Info("Calling external service: %s", serviceName)

// Log after critical operations
log.Info("Operation completed: duration=%v, result=%v", 
    time.Since(start), success)
```

### Error Context
```go
// Wrap errors with context
if err != nil {
    return nil, fmt.Errorf("failed to process user %d: %w", user.MemberId, err)
}
```

### Development vs Production
```go
// Use environment-specific behavior
if os.Getenv("ENV") == "development" {
    log.Debug("Debug info: %v", debugData)
    // Enable debug endpoints
}
```
