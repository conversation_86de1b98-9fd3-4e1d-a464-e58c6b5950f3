variables:
  DOCKER_DRIVER: "overlay2"
  IMAGE_NAME: gcr.io/$G_PROJECT_ID/${CI_PROJECT_NAME}:${CI_COMMIT_REF_NAME}-latest
  # IMAGE_TAG: ${CI_REGISTRY}/${CI_PROJECT_PATH}/${CI_COMMIT_REF_NAME}:latest
  IMAGE_URL: $CI_REGISTRY_IMAGE/${CI_COMMIT_REF_NAME}
  IMAGE_TAG: $IMAGE_URL:latest
  IMAGE_TAG_LATEST: $IMAGE_URL:latest
  MEM_LIMIT: 300m
  CPU_LIMIT: 0.5
  REGION: asia-southeast1
  DOCKER_VERSION: "26.0"
  # Enable the BuildKit engine for the Docker-in-Docker service.
  DOCKER_BUILDKIT: 1
   # Define the name for our dedicated Buildx builder instance.
  BUILDX_BUILDER_NAME: "go-app-builder"
  # Define the tag for the remote cache image. Using a separate tag for cache is a best practice.
  CACHE_IMAGE_TAG: "$CI_REGISTRY_IMAGE:$CI_COMMIT_REF_SLUG-buildcache"
  # Define the path to your Go application's Dockerfile.
  DOCKERFILE_PATH: "buildx.Dockerfile" 
  # DB_INSTANCE: uniq-187911:asia-southeast1:primary-db 


stages:
  - install
  - build
  - deploy

install_dependencies:
  image: golang:alpine
  stage: install  
  when: manual
  only:
    changes:
      - go.mod
      - go.sum
  cache:
    key: ${CI_PROJECT_NAME}
    paths:
      - .go/pkg/mod/  
  variables:
    GOPATH: $CI_PROJECT_DIR/.go 
  script:
    - echo $GOPATH
    - ls -al /
    - go mod download
    - ls -al /
    - ls -al .go

.buildImage:
  stage: build
  image: "docker:$DOCKER_VERSION"
  allow_failure: true
  services:
    - "docker:$DOCKER_VERSION-dind"
  # cache:
  #   key: ${CI_PROJECT_NAME}
  #   paths:
  #     - .go/pkg/mod/   
  before_script:
    - cat $GCLOUD_SERVICE_KEY > config/auth/gcloud-service-key.json
    - echo ${RSA_PRIV} | base64 -d > config/auth/app.rsa    
    - echo ${RSA_PUB} | base64 -d > config/auth/app.rsa.pub
    - echo ${FIREBASE_CREDENTIAL} | base64 -d > config/auth/uniq-crm-firebase-adminsdk.json
    - echo ${G_STACKDRIVE_LOGGER} | base64 -d > config/auth/google_credential_logger.json
    - echo ${G_GOOGLE_STORAGE_KEY} | base64 -d > config/auth/google_credential.json 
    - echo ${G_PUBSUB_RW_KEY} | base64 -d > config/auth/pubsub_credential.json   
  script:
    - echo $GOPATH    
    # - docker login -u ${CI_REGISTRY_USER} -p ${CI_REGISTRY_PASSWORD} ${CI_REGISTRY}
    - echo "$CI_REGISTRY_PASSWORD" | docker login $CI_REGISTRY -u $CI_REGISTRY_USER --password-stdin
    # - docker pull ${IMAGE_TAG}
    # - docker build -t ${IMAGE_TAG} --cache-from ${IMAGE_TAG} .

    # 1. Clean up any old builders to ensure a fresh start.
    - docker buildx rm "$BUILDX_BUILDER_NAME" || true
    # 2. Create a new builder using the 'docker-container' driver, which is required for remote caching.
    #    The '--use' flag makes this the default builder for all subsequent commands.
    - docker buildx create --driver docker-container --name "$BUILDX_BUILDER_NAME" --use
    
    # 3. Bootstrap the builder to ensure its container is running and ready.
    - docker buildx inspect --bootstrap

    - |
      docker buildx build \
      --platform linux/amd64 \
      --tag "$IMAGE_TAG" \
      --tag "$IMAGE_TAG_LATEST" \
      --output=type=registry \
      --cache-to "type=registry,ref=$CACHE_IMAGE_TAG,mode=max" \
      --cache-from "type=registry,ref=$CACHE_IMAGE_TAG" \
      -f "$DOCKERFILE_PATH" \
      . 


    # - docker build -t ${IMAGE_TAG} .
    # - docker push ${IMAGE_TAG}
    # - docker image tag ${IMAGE_TAG} $IMAGE_TAG_LATEST
    # - docker push $IMAGE_TAG_LATEST
  after_script:
    - docker buildx rm "$BUILDX_BUILDER_NAME" || true
    - docker logout ${CI_REGISTRY}      
  cache:
    paths:
      - vendor/

buildImageDev:
  extends: 
    - .buildImage
  rules:
    - if: $CI_PIPELINE_SOURCE == "web"
      when: manual
    - if: $SKIP_BUILD == "true"
      when: never
    - if: $CI_COMMIT_BRANCH == "dev" 
    - if: $CI_COMMIT_BRANCH =~ /^testing\/.*/      
  environment:
    name: development
  # tags:
  #   - testing-docker

buildImageStaging:
  extends: 
    - .buildImage
  rules:
    - if: $CI_PIPELINE_SOURCE == "web"
      when: manual
    - if: $SKIP_BUILD == "true"
      when: never
    - if: $CI_COMMIT_BRANCH == "staging"
  environment:
    name: staging

buildImageProduction:
  extends: 
    - .buildImage
  rules:
    - if: $CI_PIPELINE_SOURCE == "web"
      when: manual
    - if: $SKIP_BUILD == "true"
      when: never
    - if: $CI_COMMIT_BRANCH == "master"
      when: manual
  environment:
    name: production    

.deployImage:
  stage: deploy
  image: docker/compose:alpine-1.29.2
  before_script:
    - cat ${ENV} > .env
    - docker login -u ${CI_REGISTRY_USER} -p ${CI_REGISTRY_PASSWORD} ${CI_REGISTRY}
  script:
    - mkdir -p /data/crm/credentials
    - echo ${G_PUBSUB_RW_KEY} | base64 -d > /data/crm/credentials/pubsub_credential.json
    - docker pull ${IMAGE_TAG_LATEST}
    - docker container rm -f $CI_PROJECT_NAME || true
    - docker run --restart unless-stopped --name ${CI_PROJECT_NAME} --network uniq-network -v /docker/runner/data/crm:/configs/ -d -p 2322:1619 --env-file .env --log-driver=gcplogs --log-opt gcp-project=uniq-187911 -m $MEM_LIMIT --cpus=$CPU_LIMIT ${IMAGE_TAG_LATEST}


deploy_dev:
  extends: 
    - .deployImage
  environment:
    name: development
  rules:
    - if: $CI_COMMIT_BRANCH == "dev"
    - if: $CI_COMMIT_BRANCH =~ /^testing\/.*/
  variables:
    MEM_LIMIT: 500m
  tags:
    - testing-docker

deployStaging:  
  extends: 
    - .deployImage
  environment:
    name: staging
  only:
    - staging  
  # when: manual
  variables:
    MEM_LIMIT: 700m
    CPU_LIMIT: 0.8
  tags:
    - staging

deployProduction:  
  extends: 
    - .deployImage
  environment:
    name: production  
  only:
    - master    
  tags:
    - production


deploy_staging:
  stage: deploy
  image: docker/compose:1.25.5
  when: manual
  environment:
    name: staging
  only:
    - staging
  cache:
    key: ${CI_PROJECT_NAME}
    paths:
      - .go/pkg/mod/ 
  before_script:
    # - echo ${ENV_STAGING} | base64 -d > .env
    - cat $ENV > .env
    - echo ${RSA_PRIV_STAGING} | base64 -d > config/auth/app.rsa
    - echo ${RSA_PUB_STAGING} | base64 -d > config/auth/app.rsa.pub
    - echo ${FIREBASE_CREDENTIAL} | base64 -d > config/auth/uniq-crm-firebase-adminsdk.json
    - echo ${G_GOOGLE_STORAGE_KEY} | base64 -d > config/auth/google_credential.json
    - echo ${G_PUBSUB_KEY} | base64 -d > config/auth/pubsub_credential.json
    - echo ${G_PUBSUB_RW_KEY} | base64 -d > config/credentials/pubsub_credential.json
  script:
    #    - docker-compose build && docker-compose down --remove-orphans && docker-compose up -d
    - docker build -t ${CI_PROJECT_NAME} .
    - docker container rm -f ${CI_PROJECT_NAME} || true
    - docker run --name ${CI_PROJECT_NAME} -d --restart unless-stopped  -p 2322:1619 --env-file .env --log-driver=gcplogs --log-opt gcp-project=uniq-187911 $CI_PROJECT_NAME
  tags:
    - staging

deploy_production:
  stage: deploy
  image: docker/compose:1.25.5
  only:
    - master
  when: manual
  environment:
    name: production
  cache:
    key: ${CI_COMMIT_REF_NAME}
    paths:
      - .go/pkg/mod/ 
  script:
    - echo ${ENV_PRODUCTION} | base64 -d > .env
    - echo ${RSA_PRIV_PRODUCTION} | base64 -d > config/auth/app.rsa
    - echo ${RSA_PUB_PRODUCTION} | base64 -d > config/auth/app.rsa.pub
    - echo ${FIREBASE_CREDENTIAL} | base64 -d > config/auth/uniq-crm-firebase-adminsdk.json
    - echo ${G_GOOGLE_STORAGE_KEY} | base64 -d > config/auth/google_credential.json
    - echo ${G_PUBSUB_KEY} | base64 -d > config/auth/pubsub_credential.json
    - mkdir -p /data/crm/credentials
    - echo ${G_PUBSUB_RW_KEY} | base64 -d > /data/crm/credentials/pubsub_credential.json
    - docker build -t ${CI_PROJECT_NAME} .
    - docker container rm -f ${CI_PROJECT_NAME} || true
    - docker run -d --network uniq-network --restart unless-stopped --name ${CI_PROJECT_NAME} -p 2322:1619 --env-file .env --log-driver=gcplogs -v /docker/runner/data/crm:/configs/ $CI_PROJECT_NAME
  tags:
    - production

.cloudRun:
  stage: build
  image: google/cloud-sdk:alpine
  script:
    - cat $GCLOUD_SERVICE_KEY > config/auth/gcloud-service-key.json
    - echo ${RSA_PRIV} | base64 -d > config/auth/app.rsa    
    - echo ${RSA_PUB} | base64 -d > config/auth/app.rsa.pub
    - echo ${FIREBASE_CREDENTIAL} | base64 -d > config/auth/uniq-crm-firebase-adminsdk.json
    - echo ${G_STACKDRIVE_LOGGER} | base64 -d > config/auth/google_credential_logger.json
    - echo ${G_GOOGLE_STORAGE_KEY} | base64 -d > config/auth/google_credential.json 
    - echo ${G_PUBSUB_KEY} | base64 -d > config/auth/pubsub_credential.json   
    - mkdir -p config/credentials
    - echo ${G_PUBSUB_KEY} | base64 -d > config/credentials/pubsub_credential.json   
    - cat $ENV > .env
    - ls -al config/auth/
    - echo $G_GCLOUD_RUN_KEY | base64 -d > ${HOME}/gcloud-service-key.json    
    - gcloud auth activate-service-account --key-file ${HOME}/gcloud-service-key.json
    - gcloud config set project $G_PROJECT_ID
    - gcloud config set gcloudignore/enabled false
    # - gcloud builds submit --tag $IMAGE_NAME  .
    # - gcloud run deploy ${CI_PROJECT_NAME}-${CI_COMMIT_REF_NAME} --image $IMAGE_NAME --platform managed --region $REGION --allow-unauthenticated --add-cloudsql-instances=$DB_INSTANCE
    - gcloud run deploy ${CI_PROJECT_NAME}-${CI_COMMIT_REF_NAME} --source . --platform managed --region $REGION --allow-unauthenticated --add-cloudsql-instances=$DB_INSTANCE
    

deployCloudRunDev:
  extends: 
    - .cloudRun
  environment:
    name: development
  when: manual
  only:
    - dev
  variables:
    REGION: us-central1

deployCloudRunStaging:
  extends: 
    - .cloudRun
  environment:
    name: staging
  when: manual
  only:
    - staging
  variables:
    DB_INSTANCE: uniq-187911:asia-southeast1:primary-db 

deploy_staging_run:
  stage: deploy
  image: google/cloud-sdk:alpine
  only:
    - testing/run
  script:
    - echo ${ENV_STAGING} | base64 -d > .env
    - echo ${RSA_PRIV_STAGING} | base64 -d > config/auth/app.rsa
    - echo ${RSA_PUB_STAGING} | base64 -d > config/auth/app.rsa.pub
    - echo ${FIREBASE_CREDENTIAL} | base64 -d > config/auth/uniq-crm-firebase-adminsdk.json
    - echo $GCLOUD_SERVICE_KEY_STAGING | base64 -d > ${HOME}/gcloud-service-key.json
    - gcloud auth activate-service-account --key-file ${HOME}/gcloud-service-key.json
    - gcloud config set project $G_PROJECT_ID
    - gcloud config set gcloudignore/enabled false
    - gcloud container images delete $IMAGE_NAME --quiet || true
    - gcloud builds submit --tag $IMAGE_NAME  .
    - gcloud run deploy ${CI_PROJECT_NAME}-staging --image $IMAGE_NAME --platform managed --region asia-east1 --allow-unauthenticated --add-cloudsql-instances uniq-187911:asia-southeast1:primary-db --concurrency 1

deploy_production_run:
  stage: deploy
  image: google/cloud-sdk:alpine
  only:
    - master/run
  script:
    - echo ${ENV_PRODUCTION_RUN} | base64 -d > .env
    - echo ${RSA_PRIV_PRODUCTION} | base64 -d > config/auth/app.rsa
    - echo ${RSA_PUB_PRODUCTION} | base64 -d > config/auth/app.rsa.pub
    - echo ${FIREBASE_CREDENTIAL} | base64 -d > config/auth/uniq-crm-firebase-adminsdk.json
    - echo $GCLOUD_SERVICE_KEY_STAGING | base64 -d > ${HOME}/gcloud-service-key.json
    - gcloud auth activate-service-account --key-file ${HOME}/gcloud-service-key.json
    - gcloud config set project $G_PROJECT_ID
    - gcloud config set gcloudignore/enabled false
    - gcloud container images delete $IMAGE_NAME --quiet || true
    - gcloud builds submit --tag $IMAGE_NAME  .
    - gcloud run deploy ${CI_PROJECT_NAME} --image $IMAGE_NAME --platform managed --region asia-east1 --allow-unauthenticated --add-cloudsql-instances uniq-187911:asia-southeast1:primary-db --concurrency 1
