package http

import (
	"encoding/json"
	"fmt"
	"os"
	"time"

	fasthttprouter "github.com/buaazp/fasthttprouter"
	fasthttp "github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-membership/core/auth"
	"gitlab.com/uniqdev/backend/api-membership/core/cast"
	"gitlab.com/uniqdev/backend/api-membership/core/log"
	"gitlab.com/uniqdev/backend/api-membership/core/utils"
	domain "gitlab.com/uniqdev/backend/api-membership/domain"
	"gitlab.com/uniqdev/backend/api-membership/models"
	"gitlab.com/uniqdev/backend/api-membership/module/transaction"
)

type transactionHandler struct {
	uc transaction.UseCase
}

func NewHttpTransactionHandler(app *fasthttprouter.Router, useCase transaction.UseCase) {
	handler := &transactionHandler{useCase}
	app.GET("/v1/transaction/order-online-setting", auth.ValidatePublicKey(handler.FetchTransactionSetting))

	//order
	app.POST("/v2/transaction", auth.ValidatePublicKey(handler.CreateTransaction))
	app.GET("/v2/transaction/:id", auth.ValidatePublicKey(handler.FetchTransaction))
	app.POST("/v2/transaction/:id/payment", auth.ValidatePublicKey(handler.CreateTransactionPayment))
	app.GET("/v2/transaction/:id/status", auth.ValidatePublicKey(handler.FetchTransactionStatus))

	app.POST("/v2/transaction/:id/feedback", auth.ValidateToken(handler.CreateFeedback))

	//payment related
	app.GET("/v1/payment-method", auth.ValidateToken(handler.FetchPaymentMethod))

	//config related
	app.GET("/v1/transaction-config", auth.ValidateToken(handler.FetchTransactionConfig))
	app.PATCH("/v1/transaction-config", auth.ValidateToken(handler.UpdateTransactionConfig))

	//sales feedback related
	app.GET("/v1/transaction-feedback/:outletId/rating", auth.ValidatePublicKey(handler.FetchRating))
	app.GET("/v1/transaction-feedback/:outletId/review", auth.ValidatePublicKey(handler.FetchReview))
	app.POST("/v1/feedback-summary", auth.ValidateToken(handler.FetchFeedbackSummary))
	app.GET("/v1/feedback-summary-history", auth.ValidateToken(handler.FetchFeedbackSummaryHitory))

	//used for AI (like product recommendation)
	app.GET("/v1/transaction-summary", auth.ValidateDeveloperToken(handler.FetchTransactionSummary))
}

func (h *transactionHandler) FetchTransactionSetting(ctx *fasthttp.RequestCtx) {
	user := domain.UserSessionFastHttp(ctx)
	outletId := utils.ToInt(ctx.QueryArgs().Peek("outlet_id"))

	result, err := h.uc.FetchTransactionSetting(outletId, user)
	sendResponse(ctx, err, result)
}

// createTransaction
func (h *transactionHandler) CreateTransaction(ctx *fasthttp.RequestCtx) {
	var data models.TransactionCreate
	// _ = json.NewDecoder(ctx.RequestBodyStream()).Decode(&data)
	log.Info("[ori] create transaction: %s", string(ctx.Request.Body()))
	err := json.Unmarshal(ctx.Request.Body(), &data)
	if err != nil {
		log.Info("invalid body...")
		ctx.SetStatusCode(fasthttp.StatusUnprocessableEntity)
		return
	}

	user := domain.UserSessionFastHttp(ctx)
	result, err := h.uc.CreateTransaction(data, user)
	sendResponse(ctx, err, result)
}

func (h *transactionHandler) FetchTransaction(ctx *fasthttp.RequestCtx) {
	user := domain.UserSessionFastHttp(ctx)
	orderId := utils.ToString(ctx.UserValue("id"))

	result, err := h.uc.FetchTransaction(orderId, user)
	sendResponse(ctx, err, result)
}

func (h *transactionHandler) CreateTransactionPayment(ctx *fasthttp.RequestCtx) {
	param := models.PaymentRequestParam{
		Id:          cast.ToString(ctx.UserValue("id")),
		PaymentType: string(ctx.PostArgs().Peek("type")),
		PaymentId:   string(ctx.PostArgs().Peek("id")),
		Phone:       string(ctx.PostArgs().Peek("phone")),
	}
	result, err := h.uc.CreateTransactionPayment(param, domain.UserSessionFastHttp(ctx))
	sendResponse(ctx, err, result)
}

func (h *transactionHandler) CreateFeedback(ctx *fasthttp.RequestCtx) {
	salesId := utils.ToString(ctx.UserValue("id"))
	feedback := models.SalesFeedbackEntity{
		Stars:   (cast.ToFloat32(ctx.PostArgs().Peek("stars"))),
		Opinion: string(ctx.PostArgs().Peek("opinion")),
		Comment: string(ctx.PostArgs().Peek("comment")),
	}

	form, err := ctx.Request.MultipartForm()
	if !log.IfError(err) {
		for k, v := range form.Value {
			if k == "stars" {
				feedback.Stars = cast.ToFloat32(v[0])
			} else if k == "opinion" {
				feedback.Opinion = v[0]
			} else if k == "comment" {
				feedback.Comment = v[0]
			}
		}
	}

	if feedback.Comment == "" {
		return
	}

	attachmentHeader, err := ctx.FormFile("attachments")
	tempFileName := ""
	if err == nil {
		fmt.Println("feedback filename : ", attachmentHeader.Filename)
		basePath := "temp/po"
		_, err = os.Stat(basePath)
		if os.IsNotExist(err) {
			log.IfError(os.MkdirAll(basePath, os.ModePerm))
		}

		tempFileName = utils.RandStringBytes(16) + utils.ToString(time.Now().Unix()) + ".jpg"
		tempFileName = fmt.Sprintf("%s/%s", basePath, tempFileName)
		err = fasthttp.SaveMultipartFile(attachmentHeader, tempFileName)
		if log.IfError(err) {
			return
		}
		feedback.Attachments = tempFileName
	}

	err = h.uc.CreateFeedback(salesId, feedback, domain.UserSessionFastHttp(ctx))
	if err != nil {
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: err.Error()})
		return
	}

	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Message: "success"})
}

func (h *transactionHandler) FetchPaymentMethod(ctx *fasthttp.RequestCtx) {
	param := models.PaymentMethodRequestParam{
		Type:     string(ctx.QueryArgs().Peek("type")),
		OutletId: cast.ToInt(ctx.QueryArgs().Peek("outlet_id")),
	}
	result, err := h.uc.FechPaymentMethod(param, domain.UserSessionFastHttp(ctx))
	sendResponse(ctx, err, result)
}

func (h *transactionHandler) FetchTransactionConfig(ctx *fasthttp.RequestCtx) {
	result, err := h.uc.FetchTransactionConfig(domain.UserSessionFastHttp(ctx))
	sendResponse(ctx, err, result)
}

func (h *transactionHandler) UpdateTransactionConfig(ctx *fasthttp.RequestCtx) {
	var transactionConf models.TransactionConfig
	err := json.Unmarshal(ctx.PostBody(), &transactionConf)
	log.IfError(err)

	err = h.uc.UpdateTransactionConfig(transactionConf, domain.UserSessionFastHttp(ctx))
	sendResponse(ctx, err, nil)
}

func (h *transactionHandler) FetchTransactionStatus(ctx *fasthttp.RequestCtx) {
	orderId := utils.ToString(ctx.UserValue("id"))
	result, err := h.uc.FetchTransactionStatus(orderId, domain.UserSessionFastHttp(ctx))
	sendResponse(ctx, err, result)
}

func (h *transactionHandler) FetchRating(ctx *fasthttp.RequestCtx) {
	outletId := cast.ToInt(ctx.UserValue("outletId"))
	user := domain.UserSessionFastHttp(ctx)

	result, err := h.uc.FetchRating(outletId, &user)
	sendResponse(ctx, err, result)
}

func (h *transactionHandler) FetchReview(ctx *fasthttp.RequestCtx) {
	outletId := cast.ToInt(ctx.UserValue("outletId"))
	user := domain.UserSessionFastHttp(ctx)

	result, err := h.uc.FetchReview(outletId, &user)
	sendResponse(ctx, err, result)
}

func (h *transactionHandler) FetchFeedbackSummary(ctx *fasthttp.RequestCtx) {
	var param domain.FeedbackSummaryRequest
	err := json.Unmarshal(ctx.PostBody(), &param)
	if log.IfError(err) {
		sendResponse(ctx, err, nil)
		return
	}

	result, err := h.uc.FetchFeedbackSummary(param, domain.UserSessionFastHttp(ctx))
	sendResponse(ctx, err, result)
}

func (h *transactionHandler) FetchFeedbackSummaryHitory(ctx *fasthttp.RequestCtx) {
	result, err := h.uc.FetchFeedbackSummaryHitory(domain.UserSessionFastHttp(ctx))
	sendResponse(ctx, err, result)
}

func (h *transactionHandler) FetchTransactionSummary(ctx *fasthttp.RequestCtx) {
	request := domain.TransactionSummaryRequest{
		StartDate: cast.ToInt64(ctx.QueryArgs().Peek("start-date")),
	}
	result, err := h.uc.FetchTransactionSummary(request)
	sendResponse(ctx, err, result)
}
