package mysql

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"gitlab.com/uniqdev/backend/api-membership/core/array"
	"gitlab.com/uniqdev/backend/api-membership/core/cast"
	db "gitlab.com/uniqdev/backend/api-membership/core/db"
	"gitlab.com/uniqdev/backend/api-membership/core/log"
	"gitlab.com/uniqdev/backend/api-membership/core/utils"
	domain "gitlab.com/uniqdev/backend/api-membership/domain"
	"gitlab.com/uniqdev/backend/api-membership/models"
	"gitlab.com/uniqdev/backend/api-membership/module/transaction"
)

type transactionRepository struct {
	db    db.Repository
	cache domain.CacheInterface
}

func NewMysqlTransactionRepository(conn *sql.DB, cache domain.CacheInterface) transaction.Repository {
	return &transactionRepository{db.Repository{Conn: conn}, cache}
}

func (t transactionRepository) FetchOrderTypeSetting(outletId int, session domain.UserSession) ([]models.OrderConfigurationDetail, error) {
	var result []models.OrderConfigurationDetail
	query := `SELECT
	oc.*
FROM
	order_configuration_detail_ordertype_availability oc
	JOIN outlets o ON o.outlet_id = oc.outlet_fkid
WHERE
	oc.outlet_fkid = ?
	AND o.admin_fkid = ? `
	err := t.db.Prepare(query, outletId, session.AdminId).Get(&result)
	return result, err
}

func (t transactionRepository) CountTransactionToday(outletId int) (int, error) {
	sql := `select count(*) as cnt
from order_sales
where outlet_fkid = ?
AND DATE_FORMAT(from_unixtime(time_order / 1000), '%Y-%m-%d') = DATE_FORMAT(now(), '%Y-%m-%d')`
	result, err := db.Query(sql, outletId)
	if err != nil {
		return 0, err
	}
	return cast.ToInt(result["cnt"]), nil
}

func (t transactionRepository) AddTransasction(transaction models.TransactionCreate, user domain.UserSession) (int64, error) {
	var id int64
	err := db.WithTransaction(func(t db.Transaction) error {
		grandTotal := transaction.CalculateGrandTotal()
		orderSalesMap := map[string]any{
			"order_sales_id":      transaction.OrderSalesId,
			"order_type":          transaction.OrderType,
			"status":              transaction.Status,
			"outlet_fkid":         transaction.OutletID,
			"time_order":          time.Now().Unix() * 1000,
			"time_modified":       time.Now().UnixNano() / 1000000,
			"order_note":          transaction.Note,
			"items":               utils.SimplyToJson(transaction.ToSalesModel()),
			"grand_total":         grandTotal,
			"grand_total_payment": grandTotal,
		}

		// Handle member_fkid - only set if user has a valid member ID
		if user.MemberId > 0 {
			orderSalesMap["member_fkid"] = user.MemberId
		}

		// Handle customer metadata - store as JSON if customer data is provided
		if transaction.Customer.Name != "" || transaction.Customer.Phone != "" || transaction.Customer.Table != "" {
			orderSalesMap["meta_data"] = utils.SimplyToJson(transaction.Customer)
		}
		if transaction.OrderType == models.OrderTypePickup && transaction.Pickup.PickupTime != "" {
			orderSalesMap["pickup_time"] = transaction.Pickup.PickupTime
		}

		isDelivery := array.Contain([]string{models.OrderTypeInternalDelivery, models.OrderTypeDelivery}, transaction.OrderType)
		if isDelivery && transaction.Shipment.ShippingCharge > 0 {
			orderSalesMap["grand_total_payment"] = grandTotal + transaction.Shipment.ShippingCharge
		}

		resp, _ := t.Insert("order_sales", orderSalesMap)
		id, _ = resp.LastInsertId()

		//add log log
		t.Insert("order_sales_status_log", map[string]any{
			"order_sales_fkid": id,
			"order_sales_id":   transaction.OrderSalesId,
			"status":           transaction.Status,
			"admin_id":         user.AdminId,
			"time_created":     time.Now().UnixNano() / 1000000,
		})

		//add items
		for _, item := range transaction.OrderList {
			t.Insert("order_sales_item", map[string]any{
				"order_sales_fkid":    id,
				"product_detail_fkid": item.ProductDetailID,
				"qty":                 item.Qty,
			})
		}

		if transaction.Shipment.ShippingAddress != "" && isDelivery {
			shipmentData := map[string]any{
				"order_sales_fkid": id,
				"order_sales_id":   transaction.OrderSalesId,
				"shipping_address": transaction.Shipment.ShippingAddress,
				"shipping_charge":  transaction.Shipment.ShippingCharge,
				"time_created":     utils.GetCurrentMillis(),
				"time_modified":    utils.GetCurrentMillis(),
			}

			if transaction.Shipment.MemberAddressId > 0 {
				memberAddress, err := db.Query("select label, phone from members_address where members_address_id = ?", transaction.Shipment.MemberAddressId)
				log.IfError(err)
				shipmentData["shipping_name"] = memberAddress["label"]
				shipmentData["shipping_phone"] = memberAddress["phone"]
			}

			if transaction.Shipment.DeliveryTime > 0 {
				shipmentData["delivery_time"] = transaction.Shipment.DeliveryTime
			}
			if transaction.Shipment.Latitude != 0 && transaction.Shipment.Longitude != 0 {
				shipmentData["shipping_latitude"] = transaction.Shipment.Latitude
				shipmentData["shipping_longitude"] = transaction.Shipment.Longitude
			}

			t.Insert("order_sales_shipments", shipmentData)
		}
		return nil
	})
	return id, err
}

func (t transactionRepository) FetchPromotion(promotionId int) (map[string]any, error) {
	sql := `SELECT maximum_redeem_period_days, maximum_redeem_period from promotions where promotion_id=?`
	return db.Query(sql, promotionId)
}

func (t transactionRepository) CountPromotionUsed(promotionId int, daysInterval int) (int, error) {
	sql := `SELECT count(DISTINCT s.sales_id) as total_sales from sales s 
	left join sales_promotion sp on sp.sales_fkid=s.sales_id
	left join sales_detail_promotion sdp on sdp.sales_fkid=s.sales_id
	where (sp.promotion_fkid=@promoId or sdp.promotion_fkid=@promoId)
	`

	if daysInterval > 0 {
		sql += ` and s.time_created > DATE_ADD(NOW(), INTERVAL @interval HOUR) `
	}

	sql, params := db.MapParam(sql, map[string]any{
		"promoId":  promotionId,
		"interval": daysInterval * 24, // x 24: we use hour
	})

	result, err := db.Query(sql, params...)
	return cast.ToInt(result["total_sales"]), err
}

func (t transactionRepository) UpdateDeal(promotionBuyId int, status string) error {
	_, err := db.Update("promotion_buy", map[string]any{
		"status": status,
	}, "promotion_buy_id = ?", promotionBuyId)
	return err
}

func (t transactionRepository) FetchSalesFeedback(salesId string, user domain.UserSession) (models.SalesFeedbackEntity, error) {
	sql := `SELECT sf.*, s.sales_id as sales_fkid 
	from sales s 
	join outlets o on o.outlet_id=s.outlet_fkid
	left join sales_feedback sf on sf.sales_fkid=s.sales_id `

	var whereSql []string
	if salesId != "" {
		whereSql = append(whereSql, " s.sales_id = @salesId ")
	}
	if user.AdminId != 0 {
		whereSql = append(whereSql, " o.admin_fkid = @adminId ")
	}
	if user.MemberId != 0 {
		whereSql = append(whereSql, " (s.member_fkid = @memberId or s.member_fkid is null) ")
	}

	if len(whereSql) > 0 {
		sql += " where " + strings.Join(whereSql, " AND ")
	}

	sql, params := db.MapParam(sql, map[string]any{
		"salesId":  salesId,
		"adminId":  user.AdminId,
		"memberId": user.MemberId,
	})

	var result models.SalesFeedbackEntity
	err := t.db.Prepare(sql, params...).Get(&result)
	return result, err
}

func (t transactionRepository) FetchSalesFeedbackById(ids ...int) ([]models.SalesFeedbackEntity, error) {
	sql := `select * from sales_feedback `
	if len(ids) > 0 {
		sql += " where sales_feedback_id in @ids "
	}

	sql, params := db.MapParam(sql, map[string]any{
		"ids": ids,
	})

	var result []models.SalesFeedbackEntity
	err := t.db.Prepare(sql, params...).Get(&result)
	return result, err
}

func (t transactionRepository) AddSalesFeedback(feedback models.SalesFeedbackEntity) (int64, error) {
	resp, err := db.Insert("sales_feedback", feedback.ToMap())
	if err != nil {
		return 0, err
	}
	id, _ := resp.LastInsertId()
	return id, nil
}

func (t transactionRepository) UpdateSalesFeedback(feedback models.SalesFeedbackEntity) error {
	dataUpdate := make(map[string]any)

	if feedback.Attachments != "" {
		attachments := []string{feedback.Attachments}
		attachmentJson, _ := json.Marshal(attachments)
		dataUpdate["attachments"] = string(attachmentJson)
	}
	if feedback.Sentiment != "" {
		dataUpdate["sentiment"] = feedback.Sentiment
	}

	if len(dataUpdate) == 0 {
		return fmt.Errorf("UpdateSalesFeedback() no arg passed")
	}

	_, err := db.Update("sales_feedback", dataUpdate, "sales_feedback_id = ?", feedback.SalesFeedbackID)
	return err
}

func (t transactionRepository) FetchSalesFedbackNoSentiment() ([]models.SalesFeedbackEntity, error) {
	sql := `SELECT * from sales_feedback where sentiment is NULL and (comment != '' or comment is not null)`
	var result []models.SalesFeedbackEntity
	err := t.db.Prepare(sql).Get(&result)
	return result, err
}

func (t transactionRepository) FetchSales(salesId string) (models.SalesEntity, error) {
	sql := `select * from sales where sales_id = ?`

	var result models.SalesEntity
	err := t.db.Prepare(sql, salesId).Get(&result)
	return result, err
}

func (t transactionRepository) UpdateSales(sales models.SalesEntity) error {
	dataUpdate := make(map[string]any)
	if sales.MemberFkid != 0 {
		dataUpdate["member_fkid"] = sales.MemberFkid
	}

	_, err := db.Update("sales", dataUpdate, "sales_id = ?", sales.SalesID)
	return err
}

func (t transactionRepository) FetchPaymentMethod(user domain.UserSession) ([]models.PaymentMethod, error) {
	sql := `select * from order_configuration where config = 'payment_method' and  admin_id =?`

	var result []models.PaymentMethod
	orderConfig, err := t.db.Query(sql, user.AdminId).Map()
	if err != nil {
		return result, err
	}

	err = json.Unmarshal([]byte(cast.ToString(orderConfig["value"])), &result)
	return result, err
}

func (t transactionRepository) FetchOrderConfig(user domain.UserSession, configType string) ([]models.OrderConfigEntity, error) {
	sql := "select * from order_configuration where 1 "

	if user.AdminId > 0 {
		sql += " and admin_id = @adminId "
	}
	if configType != "" {
		sql += " and config = @configType "
	}

	sql, params := db.MapParam(sql, map[string]any{
		"adminId":    user.AdminId,
		"configType": configType,
	})
	var result []models.OrderConfigEntity
	err := t.db.Prepare(sql, params...).Get(&result)
	return result, err
}

func (t transactionRepository) UpdateOrderConfig(config models.OrderConfigEntity, user domain.UserSession) error {
	_, err := t.db.Update("order_configuration", map[string]any{
		"value":        config.Value,
		"time_updated": time.Now().Unix() * 1000,
	}, "config = ? and admin_id = ?", config.Config, user.AdminId)
	return err
}

func (t transactionRepository) FetchBank(user domain.UserSession, outletId int) ([]models.PaymentMediaBankEntity, error) {
	sql := `SELECT
    pmb.bank_id,
    pmb.name,
    pmb.no_rekening,
	pmb.owner
FROM
    payment_media_bank_detail pmbd
JOIN payment_media_bank pmb ON
    pmb.bank_id = pmbd.bank_fkid
WHERE
    pmbd.outlet_fkid = ? AND pmb.admin_fkid = ? AND pmb.data_status = 'on' AND LENGTH(pmb.no_rekening) > 5
	and pmbd.data_status='on' and pmbd.active_on_crm = 1 `

	var result []models.PaymentMediaBankEntity
	err := t.db.Prepare(sql, outletId, user.AdminId).Get(&result)
	return result, err
}

func (t transactionRepository) FetchOrderSales(id string, user domain.UserSession) (models.OrderSalesEntity, error) {
	sql := `select * from order_sales where order_sales_id = ? and member_fkid = ?`

	var result models.OrderSalesEntity
	err := t.db.Prepare(sql, id, user.MemberId).Get(&result)
	return result, err
}

func (t transactionRepository) FetchTransactionStatus(orderId string, user domain.UserSession) ([]models.OrderStatusEntity, error) {
	sql := `select ossl.* from order_sales_status_log  ossl 
	join order_sales os on os.id=ossl.order_sales_fkid
	where os.order_sales_id=?
	and os.member_fkid=?
	`

	var result []models.OrderStatusEntity
	err := t.db.Prepare(sql, orderId, user.MemberId).Get(&result)
	return result, err
}

func (t transactionRepository) UpdateOrderSales(order models.OrderSalesEntity) error {
	dataUpdate := make(map[string]any)
	if order.PaymentInfo != "" {
		dataUpdate["payment_info"] = order.PaymentInfo
	}
	_, err := t.db.Update("order_sales", dataUpdate, "id = ?", order.ID)
	return err
}

func (t transactionRepository) FetchRating(outletId int, user *domain.UserSession) (*models.RatingResponse, error) {
	sql := `SELECT s.outlet_fkid, round(AVG(sf.stars) , 2) as rating, count(*) as count 
	from sales_feedback  sf 
   join sales s on s.sales_id=sf.sales_fkid
   join outlets o on o.outlet_id=s.outlet_fkid
   where o.admin_fkid= @adminId and s.outlet_fkid= @outletId
   and s.data_status='on' and sf.visibility='public'
   group by s.outlet_fkid`

	sql, params := db.MapParam(sql, map[string]any{
		"adminId":  user.AdminId,
		"outletId": outletId,
	})

	var result models.RatingResponse
	err := t.db.Prepare(sql, params...).Get(&result)
	return &result, err
}

func (t transactionRepository) FetchRatingCount(outletId int, user *domain.UserSession) (*[]models.RatingResponse, error) {
	sql := `SELECT round(sf.stars) as rating, count(*) as count 
	from sales_feedback  sf 
   join sales s on s.sales_id=sf.sales_fkid
   join outlets o on o.outlet_id=s.outlet_fkid
   where o.admin_fkid= @adminId and s.outlet_fkid= @outletId
   and s.data_status='on' and sf.visibility='public'
   group by rating`

	sql, params := db.MapParam(sql, map[string]any{
		"adminId":  user.AdminId,
		"outletId": outletId,
	})

	var result []models.RatingResponse
	err := t.db.Prepare(sql, params...).Get(&result)
	return &result, err
}

func (t transactionRepository) FetchReview(outletId int, user *domain.UserSession) (*[]models.ReviewResponse, error) {
	sql := `SELECT s.sales_id, sf.stars as rating, sf.comment, sf.date_created as date_millis, s.customer_name as customer, 
	sf.attachments
	 from sales_feedback  sf 
	join sales s on s.sales_id=sf.sales_fkid
	join outlets o on o.outlet_id=s.outlet_fkid
	where o.admin_fkid= @adminId and s.outlet_fkid= @outletId
	and s.data_status='on' and sf.visibility='public' 
	order by sf.stars desc, sf.date_created desc `

	sql, params := db.MapParam(sql, map[string]any{
		"adminId":  user.AdminId,
		"outletId": outletId,
	})

	var result []models.ReviewResponse
	err := t.db.Prepare(sql, params...).Get(&result)
	return &result, err
}

func (t transactionRepository) FetchSalesItem(salesIds []string) (*[]models.TransactionItem, error) {
	sql := `SELECT CONCAT(p.name,COALESCE(CONCAT(' (', pdv.variant_name, ')'), '')) as product_name, sd.sales_fkid as sales_id 
	from sales_detail sd
	join products_detail pd on pd.product_detail_id=sd.product_detail_fkid
	left join products_detail_variant pdv on pdv.variant_id=pd.variant_fkid
	join products p on p.product_id=pd.product_fkid
	  where sd.sales_fkid  in @ids `

	sql, params := db.MapParam(sql, map[string]any{
		"ids": salesIds,
	})

	var result []models.TransactionItem
	err := t.db.Prepare(sql, params...).Get(&result)
	return &result, err
}

func (t transactionRepository) FetchUserBySales(salesId string) (domain.UserSession, error) {
	sql := `SELECT s.member_fkid as member_id, o.admin_fkid as admin_id from sales s 
	join outlets o on o.outlet_id=s.outlet_fkid
	where s.sales_id=?`

	var result domain.UserSession
	err := t.db.Prepare(sql, salesId).Get(&result)
	return result, err
}

// FetchSalesFeedbackWithFilter implements transaction.Repository.
func (t *transactionRepository) FetchSalesFeedbackWithFilter(filter models.SalesFeedbackFilter) ([]models.SalesFeedbackWithUser, error) {
	sql := `SELECT sf.*, o.admin_fkid as admin_id from sales_feedback sf 
	join sales s on s.sales_id=sf.sales_fkid
	join outlets o on o.outlet_id=s.outlet_fkid  `

	var whereSql []string
	if filter.DayInterval > 0 {
		whereSql = append(whereSql, " sf.date_created >= UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL @interval DAY)) * 1000 ")
	}
	if filter.HasComment {
		whereSql = append(whereSql, " (sf.comment is not null and sf.comment != '') ")
	}
	if filter.HasSentiment {
		whereSql = append(whereSql, " sf.sentiment is not null ")
	}
	if len(filter.FeedbackIds) > 0 {
		whereSql = append(whereSql, " sf.sales_feedback_id IN (@ids) ")
	}
	if filter.UnsetSentiment {
		whereSql = append(whereSql, " sf.sentiment IS NULL ")
	}
	if filter.StartDate > 0 && filter.EndDate > 0 {
		whereSql = append(whereSql, " sf.date_created BETWEEN @startDate AND @endDate ")
	}
	if filter.AdminId > 0 {
		whereSql = append(whereSql, " o.admin_fkid = @adminId ")
	}

	if len(whereSql) > 0 {
		sql += " where " + strings.Join(whereSql, " AND ")
	}

	sql, params := db.MapParam(sql, map[string]any{
		"interval":  filter.DayInterval,
		"ids":       filter.FeedbackIds,
		"startDate": filter.StartDate,
		"endDate":   filter.EndDate,
		"adminId":   filter.AdminId,
	})

	var result []models.SalesFeedbackWithUser
	err := t.db.Prepare(sql, params...).Get(&result)
	return result, err
}

// FetchAdmin implements transaction.Repository.
func (t *transactionRepository) FetchAdmin(adminId int) (models.Admin, error) {
	sql := `SELECT * from admin where admin_id = ?`

	var result models.Admin
	err := t.db.Prepare(sql, adminId).Get(&result)
	return result, err
}

// FetchReportRecipient implements transaction.Repository.
func (t *transactionRepository) FetchReportRecipient(adminId int, reportTypes string) ([]models.ReportRecipient, error) {
	sql := `SELECT rrt.report_type_fkid, 
	COALESCE(e.phone, if(rra.media='whatsapp', rra.address, '')) as phone,
	COALESCE(e.email, if(rra.media='email', rra.address, '')) as email, 
	rra.media, rra.address
	from report_recipient_type rrt 
	join report_type rt on rt.report_type_id=rrt.report_type_fkid
	left join report_recipient rr on rr.report_recipient_id=rrt.report_recipient_fkid
	left join report_recipient_address rra on rra.report_recipient_fkid=rr.report_recipient_id
	left join employee e on e.employee_id=rr.employee_fkid
	where rr.admin_fkid= @adminId AND rrt.report_type_fkid = @reportTypes `

	sql, params := db.MapParam(sql, map[string]any{
		"reportTypes": reportTypes,
		"adminId":     adminId,
	})

	var result []models.ReportRecipient
	err := t.db.Query(sql, params...).Model(&result)
	return result, err
}

// FetchSalesUnGivenFeedback implements transaction.Repository.
func (t *transactionRepository) FetchSalesUnGivenFeedback(receiver string, dayInterval int) ([]models.SalesEntity, error) {
	sql := `SELECT s.*
	from sales s 
	left join sales_feedback sf on sf.sales_fkid=s.sales_id
	where s.receipt_receiver=? and sf.sales_feedback_id is null
	and s.time_created > UNIX_TIMESTAMP(DATE_ADD(now(), interval -? day))*1000 `

	var result []models.SalesEntity
	err := t.db.Query(sql, receiver, dayInterval).Model(&result)
	return result, err
}

// FetchTimeoutPaymentOrder implements transaction.Repository.
func (t *transactionRepository) FetchTimeoutPaymentOrder(filter domain.TimeoutOrderFilter) ([]domain.TimeoutOrderResponse, error) {
	sql := `SELECT os.order_sales_id, os.id,     
	((UNIX_TIMESTAMP()*1000) - ossl.time_created)/60000 as diff_minutes,
	o.admin_fkid,
	md.firebase_token, md.member_fkid, ossl2.status
FROM   order_sales os
	JOIN outlets o
	  ON o.outlet_id = os.outlet_fkid
	JOIN members_detail md
	  ON os.member_fkid = md.member_fkid
		 AND o.admin_fkid = md.admin_fkid
		 join (
		 select max(id) as id, order_sales_id, max(time_created) as time_created from order_sales_status_log
group by order_sales_id
		 ) ossl on ossl.order_sales_id=os.order_sales_id
join order_sales_status_log ossl2 on ossl2.id = ossl.id 
`

	whereQuery := make([]string, 0)
	if len(filter.Status) > 0 {
		whereQuery = append(whereQuery, "ossl2.status in @status")
	}
	if filter.TimeoutThreshold > 0 {
		whereQuery = append(whereQuery, " ((UNIX_TIMESTAMP()*1000) - ossl.time_created)/60000 > @threshold ")
	}

	//build where query
	if len(whereQuery) > 0 {
		sql += " where " + strings.Join(whereQuery, " and ")
	}

	sql, params := db.MapParam(sql, map[string]any{
		"status":    filter.Status,
		"threshold": filter.TimeoutThreshold,
	})

	var result []domain.TimeoutOrderResponse
	err := t.db.Query(sql, params...).Model(&result)
	return result, err
}

// UpdateOrderSalesStatus implements transaction.Repository.
func (t *transactionRepository) UpdateOrderSalesStatus(id int, data domain.OrderSalesUpdateData) error {
	return db.WithTransaction(func(t db.Transaction) error {
		t.Update("order_sales", map[string]any{
			"status": data.Status,
		}, "id = ?", id)
		t.Insert("order_sales_status_log", map[string]any{
			"order_sales_fkid": id,
			"order_sales_id":   data.OrderSalesId,
			"admin_id":         data.AdminId,
			"status":           data.Status,
			"message":          data.Message,
			"time_created":     utils.GetCurrentMillis(),
		})
		return nil
	})
}

// FetchTransactionSummary implements transaction.Repository.
func (t *transactionRepository) FetchTransactionSummary(param domain.TransactionSummaryRequest) ([]map[string]any, error) {
	sql := `
	SELECT s.sales_id, md.members_detail_id as member_id, p.name as product_name, p.product_id, FROM_UNIXTIME(s.time_created/1000+25200) as _date,
s.outlet_fkid as outlet_id, s.time_created, sf.stars, sf.sentiment, sf.comment, sd.qty as quantity
from sales_detail sd 
join products p on sd.product_fkid=p.product_id
join sales s on s.sales_id=sd.sales_fkid
left join sales_feedback sf on sf.sales_fkid=s.sales_id
join (SELECT sales_id from sales s join outlets o on o.outlet_id=s.outlet_fkid where member_fkid is not null and s.time_created > ?) sj on sj.sales_id=sd.sales_fkid
join members m on m.member_id=s.member_fkid
join members_detail md on md.member_fkid=m.member_id and md.admin_fkid=p.admin_fkid
order by s.time_created `
	return db.QueryArray(sql, param.StartDate)
}
