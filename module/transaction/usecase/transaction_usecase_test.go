package usecase

import (
	"testing"

	"gitlab.com/uniqdev/backend/api-membership/domain"
	"gitlab.com/uniqdev/backend/api-membership/models"
	"gitlab.com/uniqdev/backend/api-membership/module/transaction"
)

func Test_transactionUseCase_GenerateTransactionId(t *testing.T) {
	type fields struct {
		repo transaction.Repository
	}
	type args struct {
		outletId int
		adminId  int
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    string
		wantErr bool
	}{
		{"2 digit", fields{}, args{29, 1}, "OM", false},
		{"2 digit (2)", fields{}, args{41, 1}, "HK", false},
		{"3 digit", fields{}, args{88, 39}, "NKI", false},
		{"4 digit", fields{}, args{603, 431}, "NMOH", false},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tr := transactionUseCase{
				repo: tt.fields.repo,
			}
			got, err := tr.GenerateTransactionId(tt.args.outletId, tt.args.adminId)
			if (err != nil) != tt.wantErr {
				t.Errorf("transactionUseCase.GenerateTransactionId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("transactionUseCase.GenerateTransactionId() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_transactionUseCase_CreateTransaction_CustomerValidation(t *testing.T) {
	type args struct {
		transaction models.TransactionCreate
		user        domain.UserSession
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
		errMsg  string
	}{
		{
			name: "authenticated user - should pass without customer data",
			args: args{
				transaction: models.TransactionCreate{
					OutletID:  1,
					OrderType: models.OrderTypeDineIn,
					OrderList: []models.OrderList{
						{ProductDetailID: 1, Price: 10000, Qty: 1},
					},
				},
				user: domain.UserSession{
					AdminId:  1,
					MemberId: 123, // authenticated user
				},
			},
			wantErr: false,
		},
		{
			name: "non-authenticated user with customer data - should pass",
			args: args{
				transaction: models.TransactionCreate{
					OutletID:  1,
					OrderType: models.OrderTypeDineIn,
					Customer: models.TransactionCustomer{
						Name:  "John Doe",
						Phone: "08123456789",
						Table: "A1",
					},
					OrderList: []models.OrderList{
						{ProductDetailID: 1, Price: 10000, Qty: 1},
					},
				},
				user: domain.UserSession{
					AdminId:  1,
					MemberId: 0, // non-authenticated user
				},
			},
			wantErr: false,
		},
		{
			name: "non-authenticated user without customer name - should fail",
			args: args{
				transaction: models.TransactionCreate{
					OutletID:  1,
					OrderType: models.OrderTypeDineIn,
					Customer: models.TransactionCustomer{
						Phone: "08123456789",
						Table: "A1",
					},
					OrderList: []models.OrderList{
						{ProductDetailID: 1, Price: 10000, Qty: 1},
					},
				},
				user: domain.UserSession{
					AdminId:  1,
					MemberId: 0, // non-authenticated user
				},
			},
			wantErr: true,
			errMsg:  "customer name is required",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tr := &transactionUseCase{}
			_, err := tr.CreateTransaction(tt.args.transaction, tt.args.user)

			if tt.wantErr {
				if err == nil {
					t.Errorf("transactionUseCase.CreateTransaction() expected error but got none")
					return
				}
				if tt.errMsg != "" && err.Error() != tt.errMsg {
					t.Errorf("transactionUseCase.CreateTransaction() error = %v, want %v", err.Error(), tt.errMsg)
				}
			} else if err != nil && !tt.wantErr {
				t.Errorf("transactionUseCase.CreateTransaction() unexpected error = %v", err)
			}
		})
	}
}
