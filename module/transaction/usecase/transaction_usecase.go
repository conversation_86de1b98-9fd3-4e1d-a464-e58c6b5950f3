package usecase

import (
	"encoding/json"
	"fmt"
	"os"
	"reflect"
	"strings"
	"time"

	v1 "gitlab.com/uniqdev/backend/api-membership/controller/v1"
	"gitlab.com/uniqdev/backend/api-membership/core/array"
	"gitlab.com/uniqdev/backend/api-membership/core/cast"
	"gitlab.com/uniqdev/backend/api-membership/core/exception"
	"gitlab.com/uniqdev/backend/api-membership/core/google"
	"gitlab.com/uniqdev/backend/api-membership/core/log"
	"gitlab.com/uniqdev/backend/api-membership/core/utils"
	domain "gitlab.com/uniqdev/backend/api-membership/domain"
	"gitlab.com/uniqdev/backend/api-membership/models"
	"gitlab.com/uniqdev/backend/api-membership/module/campaign"
	pointcollection "gitlab.com/uniqdev/backend/api-membership/module/point_collection"
	"gitlab.com/uniqdev/backend/api-membership/module/product"
	"gitlab.com/uniqdev/backend/api-membership/module/promotion"
	"gitlab.com/uniqdev/backend/api-membership/module/transaction"
	"gitlab.com/uniqdev/backend/api-membership/module/transaction/repository/nlp"
	"gitlab.com/uniqdev/backend/api-membership/module/user"
)

type transactionUseCase struct {
	repo                transaction.Repository
	repoProduct         product.Repository
	ucPointCollection   pointcollection.PointCollectionUseCase
	repoApp             domain.AppRepository
	repoUser            user.Repository
	billingService      promotion.BillingService
	repoPointCollection pointcollection.PointCollectionRepository
	repoNlp             transaction.RepositoryNlp
	repoCampaign        campaign.CampaignRepository
	repoOutlet          domain.OutletRepository
}

func NewTransactionUseCase(repository transaction.Repository, repoProduct product.Repository,
	ucPoint pointcollection.PointCollectionUseCase,
	repoApp domain.AppRepository, userRepo user.Repository, billing promotion.BillingService,
	point pointcollection.PointCollectionRepository,
	campaignRepo campaign.CampaignRepository, outletRepo domain.OutletRepository) transaction.UseCase {
	return &transactionUseCase{repository, repoProduct, ucPoint, repoApp, userRepo, billing, point,
		nlp.NewNlpRepository(), campaignRepo, outletRepo}
}

func (t *transactionUseCase) FetchTransactionSetting(outletId int, userSession domain.UserSession) (any, error) {
	orderTypeSettings, err := t.repo.FetchOrderTypeSetting(outletId, userSession)
	if err != nil {
		return nil, err
	}

	result := make(map[string]any)
	result["outlet_id"] = outletId

	resultOrderType := make(map[string]string)
	resultOrderType["self_order"] = "off"
	resultOrderType["delivery"] = "off"
	resultOrderType["pickup"] = "off"
	resultOrderType["dinein"] = "off"

	for _, orderType := range orderTypeSettings {
		resultOrderType[orderType.OrderType] = "on"
	}

	result["order_type"] = resultOrderType

	return result, nil
}

func (t *transactionUseCase) FetchTransaction(orderId string, userSession domain.UserSession) (models.TransactionCreate, error) {
	orderSales, err := t.repo.FetchOrderSales(orderId, userSession)
	if log.IfError(err) {
		return models.TransactionCreate{}, nil
	}

	transaction := orderSales.ToTransaction()
	productIds := make([]int, 0)
	for _, item := range transaction.OrderList {
		productIds = append(productIds, item.ProductDetailID)
	}

	products, err := t.repoProduct.FetchProductByDetailIds(productIds)
	log.IfError(err)

	productMap := make(map[int]map[string]any)
	for _, product := range products {
		productMap[cast.ToInt(product["product_detail_id"])] = product
	}

	for i, item := range transaction.OrderList {
		product := productMap[item.ProductDetailID]
		transaction.OrderList[i].Product = models.ItemProduct{
			Name: cast.ToString(product["name"]),
		}
	}

	//add order status log
	orderStatus, err := t.repo.FetchTransactionStatus(transaction.OrderSalesId, userSession)
	if !log.IfError(err) && len(orderStatus) > 0 {
		transaction.Message = orderStatus[len(orderStatus)-1].Message
	}

	//add shipping information
	log.Info("orderType %v: %v", transaction.OrderSalesId, transaction.OrderType)
	if array.Contain([]string{models.OrderTypeInternalDelivery, models.OrderTypeDelivery}, transaction.OrderType) {
		shipment, err := t.repo.FetchOrderShipment(transaction.OrderSalesId)
		log.IfError(err)
		transaction.Shipment = shipment
	}

	//adding transaction config
	orderConfigs, err := t.repo.FetchOrderConfig(userSession, "transaction")
	orderConfig := models.ConfigTransaction{
		PaymentTimeout: 60 * 24, //24 hours
	}
	if !log.IfError(err) && len(orderConfigs) > 0 {
		orderConfig = orderConfigs[0].ToConfigTransaction()
	}

	transaction.PaymentTimeout = orderConfig.PaymentTimeout
	transaction.OrderConfig = orderConfig

	//check if payment already timeout
	// Convert timeOrder to time.Time
	orderTime := time.Unix(0, transaction.TimeOrder*int64(time.Millisecond))
	// Calculate timeout time
	timeoutTime := orderTime.Add(time.Duration(transaction.PaymentTimeout) * time.Minute)
	// Check if current time is after timeout time
	if time.Now().After(timeoutTime) {
		transaction.Status = domain.OrderStatusCancel
		transaction.Message = "payment expired"
		go t.RunPaymentTimeout()
	}

	return transaction, nil
}

func (t *transactionUseCase) CreateTransaction(transaction models.TransactionCreate, user domain.UserSession) (map[string]any, error) {
	log.Info("create transaction: %v", utils.SimplyToJson(transaction))

	//TODO: validation
	//validation the order type value, should be either: delivery,self_order,pickup,

	orderTypeAvailable := []string{models.OrderTypeDelivery, models.OrderTypeSelfOrder, models.OrderTypePickup, models.OrderTypeDineIn, models.OrderTypeInternalDelivery}
	if !array.Contain(orderTypeAvailable, transaction.OrderType) {
		log.Info("invalid orderType: '%v'", transaction.OrderType)
		return nil, exception.WithCode{Code: 400, Message: fmt.Sprintf("invalid order type: %v", transaction.OrderType)}
	}

	// Validate customer information for non-authenticated users
	if user.MemberId == 0 {
		if transaction.Customer.Name == "" {
			log.Info("customer name is required for non-authenticated orders")
			return nil, exception.WithCode{Code: 400, Message: "customer name is required"}
		}
	}

	//TODO validate: check if the order type is enabled on the oultet

	//validate: the productId
	productDetailIds := make([]int, 0)
	for _, item := range transaction.OrderList {
		productDetailIds = append(productDetailIds, item.ProductDetailID)
	}
	produtDetails, err := t.repoProduct.FetchProductByDetailIds(productDetailIds)
	log.IfError(err)

	productDetailMap := array.FlatMapArray(produtDetails, "product_detail_id")

	isUsePromotion := transaction.Deal.PromotionBuyId > 0
	insufficientStock := make(map[int]int)
	for i, item := range transaction.OrderList {
		product := productDetailMap[cast.ToString(item.ProductDetailID)]
		if product["product_detail_id"] == 0 {
			return nil, fmt.Errorf("invalid product_detail_id: %v", item.ProductDetailID)
		}
		if item.Price == 0 && !isUsePromotion {
			transaction.OrderList[i].Price = cast.ToInt(product["price_sell"])
		} else if item.Price == 0 && item.PriceSell != 0 {
			transaction.OrderList[i].Price = item.PriceSell
		}

		if item.Qty > cast.ToInt(product["stock_qty"]) && cast.ToInt(product["stock_management"]) == 1 {
			insufficientStock[item.ProductDetailID] = cast.ToInt(product["stock_qty"])
		}
	}

	log.Info("insufficientStock data: %v", utils.SimplyToJson(insufficientStock))

	//check if promotion can be used
	if errPromoValidation := t.validatePromotion(transaction.Deal); errPromoValidation != nil {
		log.Info("error promo validation: %v", errPromoValidation)
		return nil, errPromoValidation
	}

	if transaction.OrderType == "pickup" && len(transaction.Pickup.PickupTime) != 5 {
		transaction.Pickup.PickupTime += ":00"
	}

	//status: determine the status, if manualStockCheck enable: pending, else: accept
	transaction.Status = domain.TransactionStatusPending
	// transaction.Status = "accept"
	configs, err := t.repo.FetchOrderConfig(user, "transaction")
	log.Info("transaction conf: %v", configs)
	if !log.IfError(err) && len(configs) > 0 {
		transConf := configs[0].ToConfigTransaction()
		if !transConf.StockConfirmation {
			transaction.Status = domain.TransactionStatusAccept
		}
	}

	// for accept order, make sure stock is sufficient
	if transaction.Status == domain.TransactionStatusAccept && len(insufficientStock) > 0 {
		log.Info("insufficient stock, status: %v, insufficientStock: %v", transaction.Status, insufficientStock)
		return nil, exception.WithCode{Code: exception.TransactionInsufficientStock, Message: fmt.Sprintf("stok tidak mencukupi")}
	}

	isRequiredAddress := array.Contain([]string{models.OrderTypeDelivery, models.OrderTypeInternalDelivery}, transaction.OrderType)
	if transaction.Status == domain.TransactionStatusAccept && isRequiredAddress && strings.TrimSpace(transaction.Shipment.ShippingAddress) == "" {
		log.Info("shipping address is empty, trans status %v, orderType: %v shipping address: %v", transaction.Status, transaction.OrderType, transaction.Shipment.ShippingAddress)
		return nil, exception.WithCode{Code: 400, Message: fmt.Sprintf("shipping address must be set")}
	}

	//TODO: validate shipping charge
	if transaction.Shipment.ShippingCharge > 0 {

	}

	addresses, err := t.repoUser.FetchUserAddress(domain.UserSession{MemberId: user.MemberId})
	log.Info("found %v member address of %v | try to find %v", len(addresses), user.MemberId, transaction.Shipment.MemberAddressId)
	log.IfError(err)
	for _, addrs := range addresses {
		if (transaction.Shipment.MemberAddressId > 0 && cast.ToInt(addrs["members_address_id"]) == transaction.Shipment.MemberAddressId) || (transaction.Shipment.MemberAddressId == 0 && transaction.Shipment.ShippingAddress == cast.ToString(addrs["address"])) {
			transaction.Shipment.Latitude = cast.ToFloat64(addrs["latitude"])
			transaction.Shipment.Longitude = cast.ToFloat64(addrs["longitude"])
			log.Info("set latLng, from (type: %v) %v,%v to %v,%v", reflect.TypeOf(addrs["latitude"]), addrs["latitude"], addrs["longitude"], transaction.Shipment.Latitude, transaction.Shipment.Longitude)
		}
	}

	//generate id
	transactionId, err := t.GenerateTransactionId(transaction.OutletID, int(user.AdminId))
	if log.IfError(err) {
		log.Info("failed generating transaction id")
		return nil, err
	}

	//insert
	transaction.OrderSalesId = transactionId
	id, err := t.repo.AddTransasction(transaction, user)
	if log.IfError(err) {
		return nil, err
	}

	//TODO: set the deal to be used
	if transaction.Deal.PromotionBuyId > 0 {
		log.IfError(t.repo.UpdateDeal(transaction.Deal.PromotionBuyId, "redeem"))
	}

	log.Info("transaction created %v, transactionId: %v id: %v", user.MemberId, transactionId, id)
	go t.UpdateOrderSalesItem(transaction)
	go v1.PublishOrderStatusUpdate(transactionId, "pending")
	// go UpdatePromotionBuy(sales, "redeem")

	return map[string]any{
		"id":             id,
		"order_sales_id": transactionId,
		"status":         transaction.Status,
	}, nil
}

// adjust item json data, e.g. fill product name if not sent
func (t *transactionUseCase) UpdateOrderSalesItem(transaction models.TransactionCreate) {
	// productIds := make([]int, 0)
	// for _, order := range transaction.OrderList {
	// 	if order.Product.Name == "" {
	// 		productIds = append(productIds, order.ProductDetailID)
	// 	}
	// }

	// products, err := t.repoProduct.FetchProductByDetailIds(productIds)
	// log.IfError(err)
}

func (t *transactionUseCase) GenerateTransactionId(outletId, adminId int) (string, error) {
	//get the prefix
	tmpId := outletId + adminId
	words := []string{"M", "N", "K", "O", "H", "S", "N", "I", "A", "L"}
	prefix := ""
	for i, w := range cast.ToString(tmpId) {
		fmt.Println(i, "--> rune: ", w, " : str: ", string(w), "|", words[cast.ToInt(string(w))])
		prefix += words[cast.ToInt(string(w))]
	}

	//count transaction
	countTrx, err := t.repo.CountTransactionToday(outletId)
	if err != nil {
		return "", err
	}

	dates := time.Now().UTC().Add(7 * time.Hour).Format("20060102")
	transactionId := fmt.Sprintf("%s-%s%s%d", v1.PrefixId, prefix, dates, countTrx+1)

	return transactionId, nil
}

func (t *transactionUseCase) CreateFeedback(salesId string, feedback models.SalesFeedbackEntity, user domain.UserSession) error {
	log.Info("add feedback to '%v' : %v", salesId, utils.SimplyToJson(feedback))

	if feedback.Stars < 0 || feedback.Stars > 5 {
		log.Info("stars should between 0 until 5")
		return fmt.Errorf("stars should between 0 until 5")
	}

	//fetch sales, validate if sales exist, and not given feedback
	salesFeedback, err := t.repo.FetchSalesFeedback(salesId, user)
	if err != nil {
		return err
	}

	if salesFeedback.SalesFkid == "" {
		log.Info("no transaction with id '%s'", salesId)
		return fmt.Errorf("transaction not found")
	}

	if salesFeedback.SalesFeedbackID != 0 {
		log.Info("feedback already given before! - %v", salesId)
		return fmt.Errorf("feedback already given before")
	}

	feedback.SalesFkid = salesId
	feedback.DateCreated = time.Now().Unix() * 1000
	id, err := t.repo.AddSalesFeedback(feedback)

	if err != nil {
		return err
	}

	feedback.SalesFeedbackID = int(id)
	log.Info("sales feedback '%v' inserted with id: %v", salesId, id)

	//if member id not set, set it to current user
	go t.setSalesMember(salesId, user.MemberId)
	go t.uploadSalesFeedbackAttachment(feedback, user)
	go t.UpdateFeedbackSentiment(feedback.SalesFeedbackID)
	go func(salesId string) {
		//IMPORTANT: should run point collection first, then ask for another feedback
		//askAnotherFeedback only run of point collection present
		t.ucPointCollection.RunPointCollection(user.MemberId, user.AdminId, domain.PointCollectionActionFeedback, int(id))
		t.askAnotherFeedback(salesId)
	}(salesId)

	log.Info("attach: %v", feedback.Attachments)

	return nil
}

func (t *transactionUseCase) setSalesMember(salesId string, memberId int64) {
	sales, err := t.repo.FetchSales(salesId)
	if log.IfError(err) {
		return
	}

	if sales.MemberFkid == 0 {
		sales.MemberFkid = int(memberId)
		t.repo.UpdateSales(sales)
	}
}

func (t *transactionUseCase) uploadSalesFeedbackAttachment(salesFeedback models.SalesFeedbackEntity, user domain.UserSession) {
	log.Info("attachment: '%v'", salesFeedback.Attachments)
	if salesFeedback.Attachments == "" {
		return
	}

	file, err := os.Open(salesFeedback.Attachments)
	if log.IfError(err) {
		return
	}
	defer file.Close()

	filePath := fmt.Sprintf("%s/crm/feedback/%v/%v/%v.jpg", os.Getenv("server"), user.AdminId, user.MemberId, time.Now().UnixMilli())
	url, err := google.UploadFile(file, filePath, true)
	if log.IfError(err) {
		return
	}

	//removing tmp file
	if err = os.Remove(salesFeedback.Attachments); err != nil {
		fmt.Println("Removing tmp file : ", salesFeedback.Attachments, " - error : ", err)
	}

	log.Info("uploaded file: %v ", url)
	salesFeedback.Attachments = url
	err = t.repo.UpdateSalesFeedback(salesFeedback)
	log.IfError(err)
}

func (t *transactionUseCase) FechPaymentMethod(param models.PaymentMethodRequestParam, user domain.UserSession) ([]models.PaymentMethod, error) {
	icons := map[string]models.PaymentIcon{
		"ovo": {
			Small:  "https://storage.googleapis.com/uniq-187911.appspot.com/public/icons/ovo_small.png",
			Medium: "https://storage.googleapis.com/uniq-187911.appspot.com/public/icons/ovo.png",
		},
		"gopay": {
			Small:  "https://storage.googleapis.com/uniq-187911.appspot.com/public/icons/gopay_small.png",
			Medium: "https://storage.googleapis.com/uniq-187911.appspot.com/public/icons/gopay.png",
		},
		"shopeepay": {
			Small:  "https://storage.googleapis.com/uniq-187911.appspot.com/public/icons/ShopeePay_small.png",
			Medium: "https://storage.googleapis.com/uniq-187911.appspot.com/public/icons/ShopeePay.png",
		},
		"linkaja": {
			Small:  "https://storage.googleapis.com/uniq-187911.appspot.com/public/icons/LinkAja_small.png",
			Medium: "https://storage.googleapis.com/uniq-187911.appspot.com/public/icons/LinkAja.png",
		},
		"dana": {
			Small:  "https://storage.googleapis.com/uniq-187911.appspot.com/public/icons/dana_small.png",
			Medium: "https://storage.googleapis.com/uniq-187911.appspot.com/public/icons/dana.png",
		},
		"qris": {
			Small:  "https://storage.googleapis.com/uniq-187911.appspot.com/public/icons/qris_small.png",
			Medium: "https://storage.googleapis.com/uniq-187911.appspot.com/public/icons/qris.png",
		},
	}

	result, err := t.repo.FetchPaymentMethod(user)
	if log.IfError(err) {
		return nil, err
	}

	if array.Contain([]string{"delivery", "self_order", "pickup", "dine_in"}, param.Type) {
		if param.OutletId == 0 {
			return nil, exception.WithCode{Code: 400, Message: "outlet_id is required"}
		}
		banks, err := t.repo.FetchBank(user, param.OutletId)
		log.IfError(err)

		paymentBanks := make([]models.PaymentMethodDetail, 0)
		for _, bank := range banks {
			paymentBanks = append(paymentBanks, models.PaymentMethodDetail{
				ID:            cast.ToString(bank.BankID),
				Name:          bank.Name,
				AccountName:   bank.Owner,
				AccountNumber: bank.NoRekening,
			})
		}

		if len(paymentBanks) > 0 {
			result = append(result, models.PaymentMethod{
				Type:   "bank_transfer",
				Name:   "Bank Transfer",
				Detail: paymentBanks,
			})
		}
	}

	for i, raw := range result {
		for j, detail := range raw.Detail {
			raw.Detail[j].Icon = icons[detail.ID]
		}
		result[i].Detail = raw.Detail
	}

	return result, nil
}

func (t *transactionUseCase) SetSentimentFeedback() {
	t.UpdateFeedbackSentiment()
}

func (t *transactionUseCase) UpdateFeedbackSentiment(feedbackId ...int) error {
	feedbacks, err := t.repo.FetchSalesFeedbackWithFilter(
		models.SalesFeedbackFilter{
			FeedbackIds:    feedbackId,
			HasComment:     true,
			UnsetSentiment: true,
		},
	)
	// feedbacks, err := t.repo.FetchSalesFeedbackById(feedbackId...)
	if log.IfError(err) {
		return err
	}
	log.Info("total sales feedback: %v, from ids: %v", len(feedbacks), len(feedbackId))

	repoNlp := nlp.NewNlpRepository()
	for _, fb := range feedbacks {
		if fb.Comment == "" || fb.Sentiment != "" {
			continue
		}

		result, err := repoNlp.GetSentiment(fb.Comment)
		if log.IfError(err) || result.Label == "" {
			fmt.Println("empty label....")
			continue
		}

		log.Info("%v: (%v) %v", result.Label, fb.SalesFeedbackID, fb.Comment)
		err = t.repo.UpdateSalesFeedback(models.SalesFeedbackEntity{
			SalesFeedbackID: fb.SalesFeedbackID,
			Sentiment:       result.Label,
		})
		log.IfError(err)
	}

	return nil
}

func (t *transactionUseCase) FetchTransactionConfig(user domain.UserSession) (models.TransactionConfig, error) {
	var transactionConfig models.TransactionConfig

	transactionConfs, err := t.repo.FetchOrderConfig(user, domain.OrderConfigTransaction)
	if log.IfError(err) {
		return transactionConfig, err
	}

	if len(transactionConfs) > 0 {
		var transaction models.ConfigTransaction
		log.IfError(json.Unmarshal([]byte(cast.ToString(transactionConfs[0].Value)), &transaction))
		transactionConfig.Transaction = transaction
	}

	return transactionConfig, nil

}

func (t *transactionUseCase) UpdateTransactionConfig(transactionConfig models.TransactionConfig, user domain.UserSession) error {
	transactionValue, err := json.Marshal(transactionConfig.Transaction)
	if log.IfError(err) {
		return err
	}

	orderConf := models.OrderConfigEntity{
		Config: "transaction",
		Value:  string(transactionValue),
	}
	err = t.repo.UpdateOrderConfig(orderConf, user)
	log.IfError(err)
	return nil
}

func (t *transactionUseCase) CreateTransactionPayment(param models.PaymentRequestParam, user domain.UserSession) (models.PaymentCreateResponse, error) {
	if param.PaymentType == "" || param.PaymentId == "" {
		return models.PaymentCreateResponse{}, exception.WithCode{Code: 400, Message: fmt.Sprintf("type and id are required")}
	}

	if param.PaymentType != "qris" && param.PaymentType != "ewallet" {
		return models.PaymentCreateResponse{}, exception.WithCode{Code: 400, Message: fmt.Sprintf("can not create payment with: %v", param.PaymentType)}
	}

	orderSales, err := t.repo.FetchOrderSales(param.Id, user)
	if log.IfError(err) {
		return models.PaymentCreateResponse{}, err
	}

	if orderSales.ID == 0 {
		return models.PaymentCreateResponse{}, exception.WithCode{Code: 404, Message: fmt.Sprintf("transaction not found: %v", param.Id)}
	}

	//make sure transaction status is pending
	if orderSales.Status != "accept" {
		return models.PaymentCreateResponse{}, exception.WithCode{Code: 404, Message: fmt.Sprintf("can not make payment, transaction status: %v", orderSales.Status)}
	}

	//fetch member (for authenticated users) or use customer data from meta_data (for non-authenticated users)
	var customerInfo models.Customer

	if user.MemberId > 0 {
		// Authenticated user - fetch member data
		members, err := t.repoUser.FetchMember(user)
		if log.IfError(err) {
			return models.PaymentCreateResponse{}, err
		}
		if len(members) == 0 {
			return models.PaymentCreateResponse{}, fmt.Errorf("member not found")
		}

		member := members[0]
		if param.PaymentId == "ovo" && param.Phone != "" {
			member["phone"] = param.Phone
		}

		customerInfo = models.Customer{
			Name:  cast.ToString(member["name"]),
			Phone: cast.ToString(member["phone"]),
			Email: cast.ToString(member["email"]),
		}
	} else {
		// Non-authenticated user - use customer data from meta_data
		if orderSales.MetaData != "" {
			var transactionCustomer models.TransactionCustomer
			err := json.Unmarshal([]byte(orderSales.MetaData), &transactionCustomer)
			if err != nil {
				log.Warn("Failed to parse customer meta_data: %v", err)
			}

			customerInfo = models.Customer{
				Name:  transactionCustomer.Name,
				Phone: transactionCustomer.Phone,
				Email: "", // No email for non-authenticated users
			}
		}

		// Override phone if provided in payment request (for OVO)
		if param.PaymentId == "ovo" && param.Phone != "" {
			customerInfo.Phone = param.Phone
		}

		// Validate that we have minimum required customer info
		if customerInfo.Name == "" {
			return models.PaymentCreateResponse{}, fmt.Errorf("customer information not found for payment processing")
		}
	}

	//fetch app config
	appCrm, err := t.repoApp.FetchAppInfoAndConfig(user)
	log.IfError(err)

	callbackUrl := "https://uniq.id/"
	if webUrl := appCrm.ToAppInfo().Web.Url; webUrl != "" {
		callbackUrl = fmt.Sprintf("%s/transaction/%v", webUrl, param.Id)
	}

	grandTotal := orderSales.GrandTotal
	if orderSales.GrandTotalPayment > grandTotal {
		grandTotal = orderSales.GrandTotalPayment
	}

	//get outlet detail
	outletName := ""
	if orderSales.OutletFkid > 0 {
		outlets, err := t.repoOutlet.FetchOutletName([]int{orderSales.OutletFkid})
		if err == nil && outlets != nil && len(*outlets) > 0 {
			outletName = (*outlets)[0].Name
		}
		log.IfError(err)
	}

	paymentResult, err := t.billingService.CreatePayment(models.PromotionPaymentCreate{
		AdminId:     int(user.AdminId),
		Id:          param.Id,
		Price:       grandTotal,
		PaymentType: param.PaymentId,
		CallbackUrl: callbackUrl,
		Customer:    customerInfo,
		MetaData: map[string]any{
			"Outlet": outletName,
		},
	})
	if log.IfError(err) {
		return models.PaymentCreateResponse{}, err
	}

	//save the response

	// responseData := map[string]any{
	// 	"payment_type": promotionBuyEntity.PaymentType,
	// 	"expired_at":   promotionBuyEntity.ExpiredAt,
	// 	"payment":      paymentResult.PaymentDetail(),
	// }

	log.Info("payment of '%v' -> %v", param.Id, utils.SimplyToJson(paymentResult))
	paymentInfoType := "image"
	if param.PaymentType == "ewallet" {
		paymentInfoType = "link"
	}

	//update order_sales payment info
	paymentResponse := models.PaymentCreateResponse{
		ID:   param.PaymentId,
		Type: param.PaymentType,
		PaymentInfo: models.PaymentCreateInfo{
			Value: cast.ToString(paymentResult.PaymentDetail()["url"]),
			Type:  paymentInfoType,
		},
	}
	err = t.repo.UpdateOrderSales(models.OrderSalesEntity{
		ID:          orderSales.ID,
		PaymentInfo: utils.SimplyToJson(paymentResponse),
	})
	log.IfError(err)

	return paymentResponse, nil
}

func (t *transactionUseCase) FetchTransactionStatus(orderId string, user domain.UserSession) ([]models.OrderStatusEntity, error) {
	return t.repo.FetchTransactionStatus(orderId, user)
}

// FetchTransactionSummary implements transaction.UseCase.
func (t *transactionUseCase) FetchTransactionSummary(request domain.TransactionSummaryRequest) ([]map[string]any, error) {
	log.Info("FetchTransactionSummary param: %v", utils.SimplyToJson(request))
	return t.repo.FetchTransactionSummary(request)
}
