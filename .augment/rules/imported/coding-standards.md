---
type: "agent_requested"
description: "Example description"
---

# Coding Standards for AI Code Agent

## General Principles

### 1. Separation of Concerns
- **NEVER** put business logic in handlers or repositories
- **ALWAYS** put business logic in usecases
- **NEVER** put database queries in usecases
- **ALWAYS** use repositories for data access

### 2. Dependency Direction
- Handlers depend on UseCases
- UseCases depend on Repositories
- Lower layers should not depend on higher layers
- Use interfaces to define contracts

### 3. Error Handling
- Handle errors at appropriate layers
- Use custom error types for business errors
- Log errors with context
- Return meaningful error messages

## Code Organization Rules

### File Naming Conventions
```
module/{module_name}/
├── delivery/http/{module_name}_handler.go
├── repository/mysql/{module_name}_repository.go
├── usecase/{module_name}_usecase.go
├── repository.go (interface)
└── usecase.go (interface)
```

### Package Structure
- Each module should be self-contained
- Import only necessary dependencies
- Avoid circular dependencies
- Use relative imports within the project

### Interface Definitions
```go
// In module/{module}/usecase.go
type UseCase interface {
    BusinessMethod(param domain.Request, user domain.UserSession) (domain.Response, error)
}

// In module/{module}/repository.go  
type Repository interface {
    FetchData(filter domain.Filter, user domain.UserSession) ([]models.Entity, error)
    SaveData(entity models.Entity, user domain.UserSession) (int64, error)
}
```

## Handler Layer Standards

### Request Handling
```go
func (h *handler) HandleRequest(ctx *fasthttp.RequestCtx) {
    // 1. Extract user session
    user := domain.UserSessionFastHttp(ctx)
    
    // 2. Parse request parameters
    param := parseRequestParams(ctx)
    
    // 3. Call usecase
    result, err := h.uc.BusinessMethod(param, user)
    
    // 4. Handle response
    if err != nil {
        handleError(ctx, err)
        return
    }
    
    sendSuccessResponse(ctx, result)
}
```

### Response Formatting
Always use `models.ApiResponse`:
```go
// Success response
_ = json.NewEncoder(ctx).Encode(models.ApiResponse{
    Status: true, 
    Message: "success", 
    Data: result,
})

// Error response
_ = json.NewEncoder(ctx).Encode(models.ApiResponse{
    Status: false, 
    Message: err.Error(),
    Code: errorCode,
})
```

### Parameter Extraction
```go
// Query parameters
param := string(ctx.QueryArgs().Peek("param_name"))

// Path parameters  
id := cast.ToInt(ctx.UserValue("id"))

// POST body parameters
value := string(ctx.PostArgs().Peek("field_name"))

// JSON body parsing
var request domain.RequestStruct
cast.ParseRequestFastHttp(ctx, &request)
```

## UseCase Layer Standards

### Structure Pattern
```go
type moduleUseCase struct {
    repo         Repository
    otherRepo    other.Repository
    otherUseCase other.UseCase
    service      external.Service
}

func NewModuleUseCase(repo Repository, deps ...interface{}) UseCase {
    return &moduleUseCase{
        repo: repo,
        // ... other dependencies
    }
}
```

### Business Logic Implementation
```go
func (u *moduleUseCase) BusinessMethod(param domain.Request, user domain.UserSession) (domain.Response, error) {
    // 1. Input validation
    if err := validateInput(param); err != nil {
        return nil, exception.WithCode{Code: 400, Message: err.Error()}
    }
    
    // 2. Business rules validation
    if err := u.validateBusinessRules(param, user); err != nil {
        return nil, err
    }
    
    // 3. Data retrieval
    data, err := u.repo.FetchRequiredData(param, user)
    if err != nil {
        return nil, err
    }
    
    // 4. Business logic processing
    result := u.processBusinessLogic(data, param)
    
    // 5. Data persistence (if needed)
    if param.ShouldSave {
        _, err := u.repo.SaveData(result, user)
        if err != nil {
            return nil, err
        }
    }
    
    return result, nil
}
```

### Async Operations
Use goroutines for non-critical operations:
```go
// Fire and forget operations
go u.sendNotification(user, data)
go u.updateCache(key, data)
go u.logActivity(user, action)
```

## Repository Layer Standards

### Query Construction
Always use parameterized queries with `db.MapParam()`:
```go
func (r *repository) FetchData(filter domain.Filter, user domain.UserSession) ([]models.Entity, error) {
    sql := `
        SELECT e.*, related.name 
        FROM entities e 
        LEFT JOIN related ON e.related_id = related.id
        WHERE e.admin_id = @adminId 
        AND e.status = @status
        AND e.created_date >= @startDate
    `
    
    sql, params := db.MapParam(sql, map[string]any{
        "adminId":   user.AdminId,
        "status":    filter.Status,
        "startDate": filter.StartDate,
    })
    
    var result []models.Entity
    err := r.db.Prepare(sql, params...).Get(&result)
    return result, err
}
```

### Transaction Usage
Use transactions for multi-table operations:
```go
func (r *repository) CreateComplexEntity(entity models.Entity, user domain.UserSession) (int64, error) {
    var entityId int64
    
    err := db.WithTransaction(func(t db.Transaction) error {
        // Insert main entity
        result, err := t.Insert("entities", map[string]any{
            "name":       entity.Name,
            "admin_id":   user.AdminId,
            "created_by": user.MemberId,
            "created_at": time.Now().Unix() * 1000,
        })
        if err != nil {
            return err
        }
        
        entityId, _ = result.LastInsertId()
        
        // Insert related records
        for _, detail := range entity.Details {
            _, err := t.Insert("entity_details", map[string]any{
                "entity_id": entityId,
                "value":     detail.Value,
                "type":      detail.Type,
            })
            if err != nil {
                return err
            }
        }
        
        return nil
    })
    
    return entityId, err
}
```

### Simple CRUD Operations
```go
// Fetch single record
func (r *repository) FetchById(id int, user domain.UserSession) (models.Entity, error) {
    sql := `SELECT * FROM entities WHERE id = @id AND admin_id = @adminId`
    sql, params := db.MapParam(sql, map[string]any{
        "id":      id,
        "adminId": user.AdminId,
    })
    
    var result models.Entity
    err := r.db.Prepare(sql, params...).Get(&result)
    return result, err
}

// Update record
func (r *repository) Update(id int, data map[string]any, user domain.UserSession) error {
    return db.WithTransaction(func(t db.Transaction) error {
        _, err := t.Update("entities", data, "id = ? AND admin_id = ?", id, user.AdminId)
        return err
    })
}

// Delete record
func (r *repository) Delete(id int, user domain.UserSession) error {
    return db.WithTransaction(func(t db.Transaction) error {
        _, err := t.Delete("entities", "id = ? AND admin_id = ?", id, user.AdminId)
        return err
    })
}
```

## Database Query Patterns

### Parameter Binding
Use `@paramName` for named parameters:
```go
sql := `
    SELECT * FROM products p
    JOIN categories c ON p.category_id = c.id
    WHERE p.admin_id = @adminId
    AND p.status IN @statuses
    AND p.price BETWEEN @minPrice AND @maxPrice
`

sql, params := db.MapParam(sql, map[string]any{
    "adminId":   user.AdminId,
    "statuses":  []string{"active", "featured"},
    "minPrice":  filter.MinPrice,
    "maxPrice":  filter.MaxPrice,
})
```

### Dynamic WHERE Clauses
```go
sql := `SELECT * FROM entities WHERE admin_id = @adminId $WHERE`

var whereConditions []string
paramMap := map[string]any{"adminId": user.AdminId}

if filter.Status != "" {
    whereConditions = append(whereConditions, "AND status = @status")
    paramMap["status"] = filter.Status
}

if len(filter.Categories) > 0 {
    whereConditions = append(whereConditions, "AND category_id IN @categories")
    paramMap["categories"] = filter.Categories
}

whereClause := strings.Join(whereConditions, " ")
sql = strings.Replace(sql, "$WHERE", whereClause, 1)

sql, params := db.MapParam(sql, paramMap)
```

## Error Handling Patterns

### Business Errors
```go
// In UseCase
if !isValidBusinessRule(param) {
    return nil, exception.WithCode{
        Code:    400,
        Message: "Business rule violation: specific reason",
    }
}
```

### Repository Errors
```go
// Let database errors bubble up
data, err := r.repo.FetchData(param, user)
if err != nil {
    log.Error("Repository error in BusinessMethod: %v", err)
    return nil, err
}
```

### Handler Error Response
```go
if err != nil {
    if withCode, ok := err.(exception.WithCode); ok {
        if withCode.Code >= 200 {
            ctx.SetStatusCode(withCode.Code)
        }
        _ = json.NewEncoder(ctx).Encode(models.ApiResponse{
            Status:  false,
            Code:    withCode.Code,
            Message: withCode.Message,
        })
    } else {
        ctx.SetStatusCode(fasthttp.StatusInternalServerError)
        _ = json.NewEncoder(ctx).Encode(models.ApiResponse{
            Status:  false,
            Message: err.Error(),
        })
    }
    return
}
```

## Logging Standards

### Log Levels
```go
// Info for normal operations
log.Info("Processing request for user %d", user.MemberId)

// Error for error conditions
log.Error("Failed to process: %v", err)

// Debug for development (use sparingly in production)
log.Debug("Debug info: %v", debugData)
```

### Structured Logging
```go
log.Info("Transaction created: userId=%d, transactionId=%s, amount=%d", 
    user.MemberId, transactionId, amount)
```

## Testing Guidelines

### Mock Usage
Generate mocks for interfaces:
```bash
mockery --name=Repository --dir=module/transaction --output=module/transaction/mocks
```

### Test Structure
```go
func TestUseCase_BusinessMethod(t *testing.T) {
    // Setup
    mockRepo := &mocks.Repository{}
    useCase := NewUseCase(mockRepo)

    // Test data
    param := domain.Request{Field: "value"}
    user := domain.UserSession{AdminId: 1, MemberId: 1}

    // Mock expectations
    mockRepo.On("FetchData", param, user).Return(expectedData, nil)

    // Execute
    result, err := useCase.BusinessMethod(param, user)

    // Assert
    assert.NoError(t, err)
    assert.Equal(t, expectedResult, result)
    mockRepo.AssertExpectations(t)
}
```

## Performance Guidelines

### Database Optimization
- Use appropriate indexes for query patterns
- Limit result sets with pagination
- Use caching for frequently accessed data
- Avoid N+1 query problems

### Memory Management
- Don't load large datasets into memory unnecessarily
- Use streaming for large file operations
- Clean up resources properly

### Caching Patterns
```go
// Repository level caching
func (r *repository) FetchCachedData(key string, user domain.UserSession) ([]models.Entity, error) {
    cacheKey := fmt.Sprintf("data_%d_%s", user.AdminId, key)

    // Try cache first
    if cached, err := r.cache.Get(cacheKey); err == nil && cached != "" {
        var result []models.Entity
        if json.Unmarshal([]byte(cached), &result) == nil {
            return result, nil
        }
    }

    // Fallback to database
    result, err := r.fetchFromDatabase(key, user)
    if err != nil {
        return nil, err
    }

    // Cache the result
    if data, err := json.Marshal(result); err == nil {
        r.cache.Set(cacheKey, string(data), 1*time.Hour)
    }

    return result, nil
}
```
