---
type: "agent_requested"
description: "Example description"
---

# Project Architecture Rules for AI Code Agent

## Overview
This project follows a Clean Architecture pattern with clear separation of concerns across three main layers:
- **<PERSON><PERSON> (Delivery Layer)**: HTTP request handling and response formatting
- **UseCase (Business Logic Layer)**: All business logic and orchestration
- **Repository (Data Layer)**: Database operations and data access

## Directory Structure

```
module/
├── {module_name}/
│   ├── delivery/
│   │   └── http/
│   │       └── {module}_handler.go
│   ├── repository/
│   │   └── mysql/
│   │       └── {module}_repository.go
│   ├── usecase/
│   │   └── {module}_usecase.go
│   ├── repository.go (interface)
│   └── usecase.go (interface)
domain/
├── {module}.go (domain interfaces and types)
models/
├── {entity}.go (data models and entities)
core/
├── db/ (database utilities and transaction handling)
├── auth/ (authentication middleware)
├── exception/ (error handling)
└── ... (other core utilities)
```

## Layer Responsibilities

### 1. Handler Layer (`delivery/http/`)
**Purpose**: Handle HTTP requests and responses only
**Rules**:
- Extract parameters from HTTP request (query params, path params, body)
- Validate basic request format (not business logic validation)
- Call appropriate UseCase method
- Format and return HTTP response
- Handle HTTP status codes and error responses
- Use `domain.UserSessionFastHttp(ctx)` to extract user session
- Return responses using `models.ApiResponse` structure

**Pattern**:
```go
func (h *handler) MethodName(ctx *fasthttp.RequestCtx) {
    user := domain.UserSessionFastHttp(ctx)
    // Extract parameters
    param := extractParams(ctx)
    
    // Call usecase
    result, err := h.uc.MethodName(param, user)
    if err != nil {
        ctx.SetStatusCode(fasthttp.StatusInternalServerError)
        _ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: err.Error()})
        return
    }
    
    _ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Data: result})
}
```

### 2. UseCase Layer (`usecase/`)
**Purpose**: Contain ALL business logic
**Rules**:
- Implement all business validation and rules
- Orchestrate calls to multiple repositories
- Handle complex business workflows
- Transform data between layers
- Coordinate with other usecases when needed
- Handle business-level error scenarios
- NO direct database queries - only through repositories
- NO HTTP-specific code

**Pattern**:
```go
type useCase struct {
    repo Repository
    // other dependencies (repos, usecases, services)
}

func (u *useCase) BusinessMethod(param Domain, user domain.UserSession) (Result, error) {
    // Business validation
    if err := validateBusinessRules(param); err != nil {
        return nil, err
    }
    
    // Orchestrate repository calls
    data, err := u.repo.FetchData(param, user)
    if err != nil {
        return nil, err
    }
    
    // Business logic processing
    result := processBusinessLogic(data)
    
    return result, nil
}
```

### 3. Repository Layer (`repository/mysql/`)
**Purpose**: Handle database operations ONLY
**Rules**:
- Simple database operations: SELECT, INSERT, UPDATE, DELETE
- Use parameterized queries with `db.MapParam()` function
- NO business logic - only data access
- Return raw data or simple transformations
- Handle database-specific errors
- Use transactions when needed via `db.WithTransaction()`

**Query Pattern**:
```go
func (r *repository) FetchData(param Filter, user domain.UserSession) ([]Entity, error) {
    sql := `SELECT * FROM table WHERE admin_id = @adminId AND status = @status`
    
    sql, params := db.MapParam(sql, map[string]any{
        "adminId": user.AdminId,
        "status":  param.Status,
    })
    
    var result []Entity
    err := r.db.Prepare(sql, params...).Get(&result)
    return result, err
}
```

## Database Patterns

### Query Construction
Always use the `db.MapParam()` function for parameterized queries:
```go
sql := `SELECT * FROM admin WHERE id = @salesId AND admin_id = @adminId AND member_id = @memberId`

sql, params := db.MapParam(sql, map[string]any{
    "salesId":  salesId,
    "adminId":  user.AdminId,
    "memberId": user.MemberId,
})

var result models.Entity
err := r.db.Prepare(sql, params...).Get(&result)
```

### Transaction Handling
Use `db.WithTransaction()` for operations requiring database transactions:
```go
func (r *repository) ComplexOperation(data Data, user domain.UserSession) error {
    return db.WithTransaction(func(t db.Transaction) error {
        // Insert main record
        result, err := t.Insert("main_table", map[string]any{
            "field1": data.Field1,
            "field2": data.Field2,
        })
        if err != nil {
            return err
        }
        
        id, _ := result.LastInsertId()
        
        // Insert related records
        for _, item := range data.Items {
            t.Insert("detail_table", map[string]any{
                "main_id": id,
                "value":   item.Value,
            })
        }
        
        return nil
    })
}
```

## Interface Definitions

### Domain Interfaces
Define contracts in `domain/{module}.go`:
```go
type ModuleUseCase interface {
    BusinessMethod(param Request, user UserSession) (Response, error)
}

type ModuleRepository interface {
    FetchData(filter Filter, user UserSession) ([]Entity, error)
    SaveData(data Entity, user UserSession) (int64, error)
}
```

### Module Interfaces
Define specific interfaces in `module/{module}/usecase.go` and `module/{module}/repository.go`

## Dependency Injection Pattern

In `server.go`, follow this pattern:
```go
// Repository layer
moduleRepo := mysqlModule.NewMysqlModuleRepository(dbConn, cache)

// UseCase layer (inject repositories and other dependencies)
moduleUseCase := usecaseModule.NewModuleUseCase(moduleRepo, otherRepo, otherUseCase)

// Handler layer (inject usecase)
httpModule.NewHttpModuleHandler(router, moduleUseCase)
```

## Error Handling

### Custom Errors
Use `exception.WithCode` for business errors:
```go
return nil, exception.WithCode{Code: 400, Message: "Invalid business rule"}
```

### Repository Errors
Let database errors bubble up, handle in UseCase:
```go
data, err := r.repo.FetchData(param)
if err != nil {
    log.Error("Failed to fetch data: %v", err)
    return nil, err
}
```

## Authentication & Authorization

### User Session
Always pass `domain.UserSession` containing:
- `AdminId`: Admin/tenant identifier
- `MemberId`: User identifier
- Other session data

### Middleware
Use appropriate auth middleware:
- `auth.ValidateToken()`: For authenticated endpoints
- `auth.ValidatePublicKey()`: For public endpoints

## Data Models

### Entity Models (`models/`)
- Database entity representations
- Simple data structures
- Validation tags if needed

### Domain Models (`domain/`)
- Business domain objects
- Request/Response structures
- Business logic helpers

## Testing Patterns

### Mock Generation
Use mockery for generating mocks:
```bash
mockery --name=UseCase --dir=module/transaction --output=module/transaction/mocks
```

### Test Structure
Follow the existing test patterns in `*_test.go` files
